import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/routes/app_pages.dart';
import 'package:news_lexica_app/app/core/model/main_category.dart';
import 'package:news_lexica_app/theme.dart';

import '../../../core/values/data.dart';

class MainCategoryCard extends StatelessWidget {
  const MainCategoryCard({
    super.key,
    required this.index,
    required this.list,
    required this.part,
  });
  final int index;
  final List<MainCategory> list;
  final int part;
  @override
  Widget build(BuildContext context) {
    return Obx(
      () => GestureDetector(
        onTap:
            () =>
                part == 1
                    ? Get.toNamed(
                      Routes.LESSON_CATEGORY,
                      arguments: list[index],
                    )
                    : Get.toNamed(
                      Routes.LESSON_LIST_TWO,
                      arguments: list[index],
                    ),
        child: Container(
          height: 120,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            gradient: LinearGradient(
              colors:
                  isLightTheme.value
                      ? lightColors[index %
                          lightColors.length] // Prevents out-of-range error
                      : darkColors[index % darkColors.length],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              list[index].assets == ''
                  ? Container()
                  : Image.asset("${list[index].assets}", height: 70),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "${list[index].title}",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
