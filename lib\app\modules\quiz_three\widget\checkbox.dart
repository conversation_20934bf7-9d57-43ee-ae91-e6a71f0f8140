import 'package:flutter/material.dart';

class CustomCheckboxTile extends StatefulWidget {
  final String title;
  final bool value;
  final ValueChanged<bool> onChanged;

  CustomCheckboxTile({
    required this.title,
    required this.value,
    required this.onChanged,
  });

  @override
  _CustomCheckboxTileState createState() => _CustomCheckboxTileState();
}

class _CustomCheckboxTileState extends State<CustomCheckboxTile> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        widget.onChanged(!widget.value);
      },
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          children: [
            Checkbox(
              value: widget.value,
              onChanged: (value) {
                widget.onChanged(value!);
              },
            ),
            Text(widget.title),
          ],
        ),
      ),
    );
  }
}