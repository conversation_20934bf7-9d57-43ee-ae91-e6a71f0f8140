import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/widget/custom_app_bar.dart';

import '../../../core/utils/api.dart';
import '../../../core/values/app_values.dart';
import '../controller/season_compete_info_controller.dart';

class SeasonCompeteInfoView extends BaseView<SeasonCompeteInfoController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return CustomAppBar(appBarTitleText: 'News Lexica Online Vocab Contest');
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppValues.padding),
            child: Html(
              data: '''<div class="ahS2Le">
<h2 class="F9yp7e ikZYwf LgNcQe" dir="auto" style="text-align: justify;">News Lexica Online Vocab Contest।।SEASON-1।। Registration form</h2>
</div>
<div class="cBGGJ OIC90c" dir="auto">
<div style="text-align: justify;"><strong>&nbsp;</strong></div>
<strong><u>প্রতিযোগিতায় অংশগ্রহণ প্রক্রিয়া ও নিয়মাবলী:</u></strong>
<div>&nbsp;
<div style="text-align: justify;"><strong>১। গুগল ফর্মে (<a href="https://bit.ly/ContestRegiForm">https://cutt.ly/RegiForm1</a>)&nbsp; রেজিস্ট্রেশন সম্পন্ন করতে হবে। রেজিস্ট্রেশন লিঙ্ক <a href="http://www.dreamersenglish.com">www.dreamersenglish.com</a>,&nbsp;News Lexica ফেসবুকপেজ&nbsp;(<a href="https://bit.ly/fbNewsLexica">https://cutt.ly/fb-News-Lexica</a>)এবং&nbsp;News Lexica অ্যাপে&nbsp;(<a href="https://bit.ly/newslexica">https://cutt.ly/NewsLexica</a>)&nbsp;পাওয়া যাবে</div>
<div style="text-align: justify;"><strong>২। গুগল&nbsp;ফর্মের&nbsp;মাধ্যমে দেশব্যাপী অনলাইন কনটেস্ট অনুষ্ঠিত হবে। কনটেস্টের লিঙ্ক www.dreamersenglish.com,&nbsp;News Lexica ফেসবুক পেজ এবং News Lexica&nbsp;অ্যাপে&nbsp;পাওয়া যাবে</div>
<div style="text-align: justify;"><strong>৩। </strong>পরীক্ষার ধরন MCQ (Multiple Choice Questions)</div>
<div style="text-align: justify;">&nbsp;</div>
<div style="text-align: justify;"><strong>৪।</strong> ১০০ টি MCQ ৫০ মিনিটের মধ্যে শেষ করতে হবে</div>
<div style="text-align: justify;">&nbsp;</div>
<div style="text-align: justify;"><strong>৫।</strong>গুগল প্লে স্টোর থেকে News Lexica অ্যাপ&nbsp;(<a href="https://bit.ly/newslexica">https://cutt.ly/NewsLexica</a>)&nbsp;ডাউনলোড করতে হবে</div>
<div style="text-align: justify;">&nbsp;</div>
<div style="text-align: justify;"><strong>৬।</strong> প্রতিযোগিতার সকল প্রশ্ন News Lexica অ্যাপের প্রথম 50 টি স্টোরি থেকে এবং অ্যাপ এ অন্তর্ভুক্ত বিগত বছরের বিভিন্ন চাকরির পরীক্ষায় আসা ভোকাবিউলারি থেকে নেয়া হবে&nbsp;</div>
<div style="text-align: justify;">&nbsp;</div>
<div style="text-align: justify;"><strong>৭।</strong> News Lexica ফেসবুক পেজ (<a href="https://bit.ly/fbNewsLexica">https://cutt.ly/fb-News-Lexica</a>) Follow করতে হবে। এই পেজে প্রতিযোগিতার&nbsp;সকল আপডেট নিয়মিত পোস্ট করা হবে</div>
<div style="text-align: justify;">&nbsp;</div>
<div style="text-align: justify;"><strong>৮। </strong>প্রতিযোগিতায়&nbsp;পুরস্কারের&nbsp; জন্য দুই বা ততোধিক মনোনীত প্রতিযোগীদের কুইজের নাম্বার সমান হলে যিনি দ্রুততম সময়ের মধ্যে পরীক্ষা শেষ করবেন তিনি অগ্রগামী হবেন। সময়ও সমান হলে লটারির মাধ্যমে বিজয়ী নির্ধারণ হবে। এক্ষেত্রে News Lexica কর্তৃপক্ষের সিদ্ধান্ত চূড়ান্ত বলে গণ্য হবে</div>
<div style="text-align: justify;">&nbsp;</div>
<div style="text-align: justify;"><strong>৯।</strong> প্রতিযোগিতায় বিজয়ীদের নাম জাতীয় পত্রিকায় এবং পুরস্কার বিতরণের তারিখ ওয়েবসাইট ও ফেসবুক পেজে প্রকাশ করা হবে</div>
<div style="text-align: justify;">&nbsp;</div>
<div style="text-align: justify;"><strong>১০।</strong> প্রতিযোগিতা সমাপ্ত হওয়ার ৭ দিনের মধ্যে বিজয়ীদের মধ্যে আনুষ্ঠানিকভাবে পুরস্কার বিতরণ হবে।</div>
<div style="text-align: justify;">&nbsp;</div>
<div style="text-align: justify;"><strong>১১</strong>। অনিবার্য কারণঃবশত প্রতিযোগিতার তারিখ পেছানোর প্রয়োজন হলে তা এই ফর্মে, ফেসবুক পেজ ও এপে জানিয়ে দেয়া হবে।</div>
<div style="text-align: justify;">&nbsp;</div>
<div style="text-align: justify;">প্রতিযোগিতা, রেজিট্রেশন ও এ্যাপ সাবস্ক্রিপশন&nbsp; সম্পর্কে আরো জিজ্ঞাসা&nbsp; থাকলে আমাদের ইমেইল করুন <EMAIL> এই ঠিকানায় অথবা হোয়াটসঅ্যাপ করুন 01860 50 79 13 নম্বরে।</div>
</div>
</div>''',
              onLinkTap:
                  (String? url, Map<String, String> attributes, element) {
                Api().launchURL(url!);
              },
            ),
          ),
        ],
      ),
    );
    throw UnimplementedError();
  }
}
