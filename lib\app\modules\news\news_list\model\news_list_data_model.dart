import 'package:flutter/cupertino.dart';

class NewsListDataModel {
  String title;
  String description;
  String url;
  String image;

  NewsListDataModel({
    required this.title,
    required this.description,
    required this.url,
    required this.image,
  });

  factory NewsListDataModel.fromJson(Map<String, dynamic> json) {
    return NewsListDataModel(
      title: json['title'],
      description: json['description'],
      url: json['url'],
      image: json['image'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'url': url,
      'image': image,
    };
  }
}
