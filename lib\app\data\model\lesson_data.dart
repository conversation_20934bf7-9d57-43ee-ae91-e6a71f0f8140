class LessonData {
  List<Message>? message;

  LessonData({this.message});

  LessonData.fromJson(Map<String, dynamic> json) {
    if (json['message'] != null) {
      message = <Message>[];
      json['message'].forEach((v) {
        message!.add(new Message.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.message != null) {
      data['message'] = this.message!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Message {
  int? lessonNumber;
  String? category;
  String? title;
  String? pressName;
  int? id;

  Message(
      {this.lessonNumber, this.category, this.title, this.pressName, this.id});

  Message.fromJson(Map<String, dynamic> json) {
    lessonNumber = json['lessonNumber'];
    category = json['category'];
    title = json['title'];
    pressName = json['pressName'];
    id = json['id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['lessonNumber'] = this.lessonNumber;
    data['category'] = this.category;
    data['title'] = this.title;
    data['pressName'] = this.pressName;
    data['id'] = this.id;
    return data;
  }
}
