import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/model/update_user_pass_body_prem.dart';
import 'package:news_lexica_app/app/core/model/update_user_score_body_prem.dart';
import 'package:news_lexica_app/app/data/model/score_board_data_response.dart';
import 'package:news_lexica_app/app/data/model/user_data.dart';
import 'package:news_lexica_app/app/data/remote/user_remote_data_source.dart';
import 'package:news_lexica_app/app/data/repository/user_repository.dart';

import '../../core/model/score_query_prem.dart';

class UserRepositoryImpl implements UserRepository {
  final UserRemoteDataSrc _remoteSource = Get.find(
    tag: (UserRemoteDataSrc).toString(),
  );
  @override
  Future<UserData> getUserData(String userId) {
    return _remoteSource.getUserData(userId);
    throw UnimplementedError();
  }

  @override
  Future<ScoreBoardDataResponse> getScoreBoard(ScoreQueryPrem scoreQueryPrem) {
    return _remoteSource.getScoreBoard(scoreQueryPrem);
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> updateUserScore(
    UpdateUserScoreBodyPrem updateUserScoreBodyPrem,
  ) {
    return _remoteSource.updateUserScore(updateUserScoreBodyPrem);
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> getUserScore(String userId) {
    return _remoteSource.getUserScore(userId);
    throw UnimplementedError();
  }
}
