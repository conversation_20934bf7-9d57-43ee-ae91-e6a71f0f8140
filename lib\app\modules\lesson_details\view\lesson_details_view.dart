import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/widget/custom_app_bar.dart';
import 'package:news_lexica_app/app/modules/lesson_details/controller/lesson_details_controller.dart';
import 'package:news_lexica_app/app/routes/app_pages.dart';

class LessonDetailsView extends BaseView<LessonDetailsController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return ListView(
      children: [
        SizedBox(
          height: 220,
          child: Stack(
            children: [
              Container(color: Colors.teal[100], height: 200),
              Positioned(
                bottom: 0,
                right: 0,
                left: 0,
                child: InkWell(
                  onTap: () {
                    controller.isGlowing.value
                        ? controller.isGlowing(false)
                        : null;
                    Get.toNamed(
                      Routes.LESSON_READING,
                      arguments: controller.lessonUiData,
                    );
                  },
                  child: Obx(
                    () => AnimatedContainer(
                      duration: const Duration(
                        milliseconds: 300,
                      ), // Smooth transition
                      decoration: BoxDecoration(
                        boxShadow:
                            controller.isGlowing.value
                                ? [
                                  BoxShadow(
                                    color: Colors.blueAccent.withOpacity(0.6),
                                    blurRadius: 20,
                                    spreadRadius: 2,
                                  ),
                                ]
                                : null,
                      ),
                      child: Card(
                        // Dark card background
                        margin: const EdgeInsets.all(10),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 50,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "Read the story",
                                    style: Theme.of(
                                      context,
                                    ).textTheme.titleLarge!.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 28,
                                    ),
                                  ),
                                  Image.asset(
                                    'assets/lesson_read.png',
                                    height: 50,
                                  ),
                                ],
                              ),
                              const SizedBox(height: 20),
                              const Text(
                                "Participate the quizzes to test your word power.",
                                style: TextStyle(color: Colors.white),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        Card(
          margin: const EdgeInsets.all(10),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 20),
            child: ListTile(
              onTap: () {
                Get.toNamed(
                  Routes.LESSON_QUIZ_ONE,
                  arguments: controller.quizOneList,
                );
              },
              leading: Image.asset('assets/word.png'),
              title: const Text(
                "Quiz 1",
                style: TextStyle(color: Colors.white),
              ),
              subtitle: const Text(
                "Play with Words",
                style: TextStyle(color: Colors.white),
              ),
              trailing: Obx(
                () => Text(
                  "${controller.quizOneList.length}",
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
          ),
        ),

        Card(
          margin: const EdgeInsets.all(10),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 20),
            child: ListTile(
              onTap: () {
                Get.toNamed(
                  Routes.LESSON_QUIZ_TWO,
                  arguments: controller.quizTwoList,
                );
              },
              leading: Image.asset('assets/lesson_quiz2.png'),
              title: const Text(
                "Quiz 2",
                style: TextStyle(color: Colors.white),
              ),
              subtitle: const Text(
                "Memory booster",
                style: TextStyle(color: Colors.white),
              ),
              trailing: Obx(
                () => Text(
                  "${controller.quizTwoList.length}",
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
          ),
        ),

        Card(
          margin: const EdgeInsets.all(10),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 20),
            child: ListTile(
              onTap: () {
                Get.toNamed(
                  Routes.LESSON_QUIZ_THREE,
                  arguments: controller.quizThreeList,
                );
              },
              leading: Image.asset('assets/lesson_quiz3.png'),
              title: const Text(
                "Quiz 3",
                style: TextStyle(color: Colors.white),
              ),
              subtitle: const Text(
                "Advanced learners’ quiz for Competitive exams",
                style: TextStyle(color: Colors.white),
              ),
              trailing: Obx(
                () => Text(
                  "${controller.quizThreeList.length}",
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
          ),
        ),

        Card(
          margin: const EdgeInsets.all(10),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 10),
            child: ListTile(
              onTap: () {
                Get.toNamed(
                  Routes.LESSON_QUIZ_DISCUSSION,
                  arguments: controller.lessonUiData.id,
                );
              },
              leading: Image.asset('assets/lesson_discussion.png'),
              title: const Text(
                "Discussion",
                style: TextStyle(color: Colors.white),
              ),
              subtitle: const Text(
                "Discuss with other users",
                style: TextStyle(color: Colors.white),
              ),
            ),
          ),
        ),
      ],
    );
    throw UnimplementedError();
  }
}
