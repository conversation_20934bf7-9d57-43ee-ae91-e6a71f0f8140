import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import 'package:news_lexica_app/app/data/model/lesson_data_details.dart';
import 'package:news_lexica_app/app/modules/lesson_details/model/quiz_one_ui.dart';
import 'package:news_lexica_app/app/modules/lesson_details/model/quiz_two_ui_data.dart';
import 'dart:convert';
import '../../../data/repository/lesson_repository.dart';
import '../../home/<USER>/lesson_ui_data.dart';
import '../model/quiz_three_ui_data.dart';
import 'package:flutter/services.dart';

class LessonDetailsController extends BaseController {
  final LessonRepository _repository = Get.find(
    tag: (LessonRepository).toString(),
  );

  var isGlowing = true.obs;

  final Rx<LessonUiData> _lessonUiDataController = Rx(LessonUiData());
  LessonUiData get lessonUiData => _lessonUiDataController.value;

  final RxList<QuizOneUiData> _quizOneListController = RxList.empty();

  List<QuizOneUiData> get quizOneList => _quizOneListController.toList();

  final RxList<QuizTwoUiData> _quizTwoListController = RxList.empty();

  List<QuizTwoUiData> get quizTwoList => _quizTwoListController.toList();

  final RxList<QuizThreeUiData> _quizThreeListController = RxList.empty();

  List<QuizThreeUiData> get quizThreeList => _quizThreeListController.toList();

  final RxList<QuizOneUiData> quizOneMistakeListController = RxList.empty();

  List<QuizOneUiData> get quizOneMistakeList =>
      quizOneMistakeListController.toList();

  final RxList<QuizTwoUiData> quizTwoMistakeListController = RxList.empty();

  List<QuizTwoUiData> get quizTwoMistakeList =>
      quizTwoMistakeListController.toList();

  final RxList<QuizThreeUiData> quizThreeMistakeListController = RxList.empty();

  List<QuizThreeUiData> get quizThreeMistakeList =>
      quizThreeMistakeListController.toList();

  void getLessonDetails(int id) {
    var future = _repository.getLessonDataDetails(id);

    callDataService(
      future,
      onSuccess: _handleLessonDetailsSuccess,
      onError: _handleLessonDetailsError,
    );
  }

  Future<void> getOfflineLessonDetails(int lessonNumber) async {
    final String response = await rootBundle.loadString(
      'assets/lesson_$lessonNumber.json',
    );
    final data = await json.decode(response);
    var freeLesson = LessonDetailsData.fromJson(data);
    _handleLessonDetailsSuccess(freeLesson);
  }

  _handleLessonDetailsSuccess(LessonDetailsData response) {
    _lessonUiDataController(
      LessonUiData(
        id: response.message?.id != null ? response.message?.id! : -1,
        category: response.message?.category ?? "",
        publishDate: response.message?.publishDate ?? "",
        audio: response.message?.audio ?? '',
        lessonTitle: response.message?.lessonTitle ?? '',
        contentType: response.message?.contentType ?? 0,
        paragraph: response.message?.paragraph ?? '',
        lessonNumber: response.message?.lessonNumber ?? 0,
        picture: response.message?.picture ?? "",
        part: response.message?.part ?? 0,
        title: response.message?.title ?? "",
        pressName: response.message?.pressName ?? "",
      ),
    );

    List<QuizOneUiData>? quizOneList =
        response.message?.quizOne
            ?.map(
              (e) => QuizOneUiData(
                id: e.id != null ? e.id! : 0,
                answer: e.answer != null ? e.answer! : "Null",
                lessonId: e.lessonId != null ? e.lessonId! : 0,
                option1: e.option1 != null ? e.option1! : "Null",
                option2: e.option2 != null ? e.option2! : "Null",
                option3: e.option3 != null ? e.option3! : "Null",
                option4: e.option4 != null ? e.option4! : "Null",
                question: e.question != null ? e.question! : "Null",
              ),
            )
            .toList();

    _quizOneListController(quizOneList);

    List<QuizTwoUiData>? quizTwoList =
        response.message?.quizTwo
            ?.map(
              (e) => QuizTwoUiData(
                question: e.question != null ? e.question! : "Null",
                answer: e.answer != null ? e.answer! : "Null",
                lessonId: e.lessonId != null ? e.lessonId! : 0,
                id: e.id != null ? e.id! : 0,
              ),
            )
            .toList();

    _quizTwoListController(quizTwoList);

    List<QuizThreeUiData>? quizThreeList =
        response.message?.quizThree
            ?.map(
              (e) => QuizThreeUiData(
                id: e.id != null ? e.id! : 0,
                answer: e.answer != null ? e.answer! : "Null",
                lessonId: e.lessonId != null ? e.lessonId! : 0,
                option1: e.option1 != null ? e.option1! : "Null",
                option2: e.option2 != null ? e.option2! : "Null",
                option3: e.option3 != null ? e.option3! : "Null",
                option4: e.option4 != null ? e.option4! : "Null",
                question: e.question != null ? e.question! : "Null",
              ),
            )
            .toList();

    _quizThreeListController(quizThreeList);
  }

  _handleLessonDetailsError(Exception exception) {}

  @override
  void onInit() {
    var dataModel = Get.arguments;
    if (dataModel is int) {
      const offlineModels = {1, 6, 16, 26, 39, 85};

      if (offlineModels.contains(dataModel)) {
        getOfflineLessonDetails(dataModel);
      } else {
        getLessonDetails(dataModel);
      }
    }
    super.onInit();
  }
}
