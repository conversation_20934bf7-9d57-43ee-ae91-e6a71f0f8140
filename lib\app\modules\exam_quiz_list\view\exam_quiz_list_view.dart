import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/values/app_values.dart';
import 'package:news_lexica_app/app/core/widget/custom_app_bar.dart';
import 'package:news_lexica_app/app/core/widget/elevated_container.dart';
import 'package:news_lexica_app/app/routes/app_pages.dart';

import '../controller/exam_quiz_list_controller.dart';

class ExamQuizListView extends BaseView<ExamQuizListController> {
  ExamQuizListView() {}
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return CustomAppBar(appBarTitleText: 'Job Vocabulary Quizzes');
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(AppValues.padding),
      child: AnimationLimiter(
        child: ListView.builder(
          itemCount: 140,
          itemBuilder: (context, index) {
            return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(milliseconds: 800),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: AppValues.halfPadding,
                    ),
                    child: ElevatedContainer(
                      bgColor: Theme.of(context).cardColor,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: AppValues.padding,
                        ),
                        child: ListTile(
                          onTap:
                              () =>
                                  index < 5
                                      ? Get.toNamed(
                                        Routes.EXAM_QUIZ_DETAILS,
                                        arguments: index + 1,
                                      )
                                      : controller.isSubscribed.value
                                      ? Get.toNamed(
                                        Routes.EXAM_QUIZ_DETAILS,
                                        arguments: index + 1,
                                      )
                                      : Fluttertoast.showToast(
                                        msg: "Your not subscribed",
                                      ),
                          leading:
                              index < 5
                                  ? Image.asset("assets/exam-time.png")
                                  : Obx(
                                    () =>
                                        controller.isSubscribed.value
                                            ? Image.asset(
                                              "assets/exam-time.png",
                                            )
                                            : Icon(Icons.lock),
                                  ),
                          title: Text(
                            'Quiz ${index + 1}',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          subtitle: const Text(
                            "Previous Years' Job Vocabulary",
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
    throw UnimplementedError();
  }
}
