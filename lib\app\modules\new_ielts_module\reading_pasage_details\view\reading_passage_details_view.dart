import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/modules/home/<USER>/lesson_ui_data.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/reading_pasage_details/controller/reading_passage_details_controller.dart';

import '../../../../routes/app_pages.dart';

class ReadingPassageDetailsView
    extends BaseView<ReadingPassageDetailsController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 220,
          child: Stack(
            children: [
              Container(
                color: Colors.teal[100],
                height: 200,
              ),
              Positioned(
                bottom: 0,
                right: 0,
                left: 0,
                child: InkWell(
                  onTap: () {
                    Get.toNamed(Routes.IELTS_STORY_READING_PAGE,
                        arguments: LessonUiData(
                          paragraph: controller.readingPassageDetails.value
                              .passage?[0].paragraph,
                          lessonTitle: controller
                              .readingPassageDetails.value.passage![0].title,
                        ));
                  },
                  child: AnimatedContainer(
                    duration:
                        const Duration(milliseconds: 300), // Smooth transition
                    decoration: BoxDecoration(
                      boxShadow: true
                          ? [
                              BoxShadow(
                                color: Colors.blueAccent.withOpacity(0.6),
                                blurRadius: 20,
                                spreadRadius: 2,
                              ),
                            ]
                          : null,
                    ),
                    child: Card(
                      // Dark card background
                      margin: const EdgeInsets.all(10),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 50),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Obx(() => Text(
                                      controller.readingPassageDetails.value
                                              .passage?[0].title ??
                                          "",
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleLarge!
                                          .copyWith(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 28),
                                    )),
                                Image.asset(
                                  'assets/lesson_read.png',
                                  height: 50,
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),
                            Text(
                              "Read the passage carefully",
                              style: TextStyle(color: Colors.white),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
        Card(
            margin: const EdgeInsets.all(10),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 20),
              child: ListTile(
                onTap: () {
                  Get.toNamed(
                    Routes.IELTS_NEW_QUIZ_EXMPLE,
                    arguments: controller
                        .readingPassageDetails.value.ieltsReadingPassageExample,
                  );
                },
                leading: Image.asset(
                  'assets/book.png',
                ),
                title: Obx(() => Text(
                    controller.readingPassageDetails.value.name == null
                        ? ""
                        : "Vocabulary from ${controller.readingPassageDetails.value.name}",
                    style: Theme.of(context)
                        .textTheme
                        .titleLarge
                        ?.copyWith(color: Colors.white))),
                subtitle: const Text("", style: TextStyle(color: Colors.white)),
                // trailing: Obx(() => Text("3",
                //     style: TextStyle(color: Colors.white))),
              ),
            )),
        Card(
            margin: const EdgeInsets.all(10),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 20),
              child: ListTile(
                onTap: () {
                  Get.toNamed(
                    Routes.IELTS_NEW_QUIZ_ONE,
                    arguments: controller
                        .readingPassageDetails.value.ieltsReadingPassageQuizOne,
                  );
                },
                leading: Image.asset(
                  'assets/word.png',
                ),
                title: Text("Quiz 1",
                    style: Theme.of(context)
                        .textTheme
                        .titleLarge
                        ?.copyWith(color: Colors.white)),
                subtitle: const Text("", style: TextStyle(color: Colors.white)),
                // trailing: Obx(() => Text("3",
                //     style: TextStyle(color: Colors.white))),
              ),
            )),
        Card(
            margin: const EdgeInsets.all(10),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 20),
              child: ListTile(
                onTap: () {
                  Get.toNamed(Routes.IELTS_NEW_QUIZ_TWO,
                      arguments: controller.readingPassageDetails.value
                          .ieltsReadingPassageQuizTwo);
                },
                leading: Image.asset(
                  'assets/lesson_quiz2.png',
                ),
                title: Text("Quiz 2",
                    style: Theme.of(context)
                        .textTheme
                        .titleLarge
                        ?.copyWith(color: Colors.white)),
                subtitle: const Text("", style: TextStyle(color: Colors.white)),
              ),
            )),
      ],
    );
    throw UnimplementedError();
  }
}
