import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';

import '../controller/forget_pass_controller.dart';

class ForgetPassView extends BaseView<ForgetPassController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: Form(
        //  key: formKey,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 25),
              Image.asset('assets/forgot-password.png', height: 100),
              <PERSON><PERSON><PERSON><PERSON>(height: 25),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Forgot Password?",
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  SizedBox(height: 15),
                  Text("Please enter the mobile number for changing password"),
                ],
              ),
              SizedBox(height: 25),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text("Mobile No"),
                  const SizedBox(height: 3),

                  TextFormField(
                    controller: controller.phoneController,
                    keyboardType: TextInputType.phone,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter mobile number';
                      } else if (value.length != 11) {
                        return 'Please enter a valid number';
                      }
                      return null;
                    },

                    decoration: InputDecoration(
                      prefixIconConstraints: const BoxConstraints(
                        minHeight: 50,
                        minWidth: 50,
                      ),
                      prefixIcon: Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: Container(
                          decoration: const BoxDecoration(
                            color: Color(0xffe2e2e2),
                            borderRadius: BorderRadius.only(
                              bottomLeft: Radius.circular(5),
                              topLeft: Radius.circular(5),
                            ),
                          ),
                          child: const Padding(
                            padding: EdgeInsets.all(15.0),
                            child: Text("+88"),
                          ),
                        ),
                      ),

                      contentPadding: const EdgeInsets.only(
                        left: 10.0,
                        top: 12.0,
                        bottom: 12.0,
                      ),
                      errorBorder: OutlineInputBorder(
                        borderSide: const BorderSide(
                          width: 1.0,
                          color: Color(0xffe2e2e2),
                        ),
                        borderRadius: BorderRadius.circular(5),
                      ),

                      focusedErrorBorder: OutlineInputBorder(
                        borderSide: const BorderSide(
                          width: 1.0,
                          color: Color(0xffe2e2e2),
                        ),
                        borderRadius: BorderRadius.circular(5),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderSide: const BorderSide(
                          width: 1.0,
                          color: Color(0xffe2e2e2),
                        ),
                        borderRadius: BorderRadius.circular(5),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(5),
                        borderSide: const BorderSide(
                          width: 1.0,
                          color: Color(0xffe2e2e2),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 25),

              ElevatedButton(
                onPressed: () {
                  controller.checkUserExist(controller.phoneController.text);
                },
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size.fromHeight(40),
                  backgroundColor: Colors.teal,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5),
                  ),
                ),
                child: Text(
                  "Check Phone Number",
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
    throw UnimplementedError();
  }
}
