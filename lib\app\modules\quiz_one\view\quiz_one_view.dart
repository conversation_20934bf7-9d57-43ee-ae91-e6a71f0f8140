import 'dart:math';

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/values/app_values.dart';
import 'package:news_lexica_app/app/core/values/text_styles.dart';
import 'package:news_lexica_app/app/core/widget/custom_app_bar.dart';

import '../controller/quiz_one_controller.dart';

class QuizOneView extends BaseView<QuizOneController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return PopScope(
        onPopInvoked: (bool didPop) {
          if (didPop) {
            return;
          }
          closeDialog();
        },
        canPop: false,
        child: Column(
          children: [
            SizedBox(
              height: AppValues.extraLargePadding,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                    onPressed: () {
                      closeDialog();
                    },
                    icon: Icon(
                      Icons.close,
                      size: 29,
                    )),
                Obx(
                  () => Center(
                    child: Text(
                      "Score ${controller.list.where((p0) => p0 == Answer.right).length}/${controller.quiz3.length}",
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                  ),
                ),
                Container()
              ],
            ),
            Text("নিচের words এর সঠিক অর্থ টেপ করুন:"),
            SizedBox(
              height: AppValues.largePadding,
            ),
            Obx(
              () => controller.quiz3.isEmpty
                  ? Container()
                  : controller.currentIndex.value == controller.quiz3.length
                      ? Container()
                      : Padding(
                          padding: const EdgeInsets.only(left: 10, right: 10),
                          child: Card(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: AppValues.largePadding,
                                  vertical: AppValues.padding),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "${controller.quiz3[controller.currentIndex.value].question}",
                                    style:
                                        Theme.of(context).textTheme.titleLarge,
                                  ),
                                  Container()
                                ],
                              ),
                            ),
                          ),
                        ),
            ),
            SizedBox(
              height: AppValues.largePadding,
            ),
            Obx(() => controller.quiz3.isEmpty
                ? Container()
                : controller.currentIndex.value == controller.quiz3.length
                    ? Container()
                    : Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Obx(
                                () => InkWell(
                                  onTap: () async {
                                    int randomNumber =
                                        controller.random.nextInt(12) + 1;
                                    if (controller.quizCheckAnswer(
                                        "${controller.quiz3[controller.currentIndex.value].choices[0]}",
                                        controller.quiz3[
                                            controller.currentIndex.value])) {
                                      await controller.player.play(AssetSource(
                                          'audio/correct${randomNumber}.mp3'));
                                      controller.list[controller
                                          .currentIndex.value] = Answer.right;
                                      // controller.increaseScore();
                                    } else {
                                      await controller.player
                                          .play(AssetSource('audio/wrong.mp3'));
                                      controller.list[controller
                                          .currentIndex.value] = Answer.wrong;
                                    }
                                  },
                                  child: Card(
                                    child: SizedBox(
                                      width: Get.width * .45,
                                      height: 120,
                                      child: Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Center(
                                            child: Text(
                                          "${controller.quiz3[controller.currentIndex.value].choices[0]}",
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleLarge,
                                        )),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Obx(
                                () => InkWell(
                                  onTap: () async {
                                    int randomNumber =
                                        controller.random.nextInt(12) + 1;
                                    if (controller.quizCheckAnswer(
                                        "${controller.quiz3[controller.currentIndex.value].choices[1]}",
                                        controller.quiz3[
                                            controller.currentIndex.value])) {
                                      await controller.player.play(AssetSource(
                                          'audio/correct${randomNumber}.mp3'));
                                      controller.list[controller
                                          .currentIndex.value] = Answer.right;
                                      // controller.increaseScore();
                                    } else {
                                      await controller.player
                                          .play(AssetSource('audio/wrong.mp3'));
                                      controller.list[controller
                                          .currentIndex.value] = Answer.wrong;
                                    }
                                  },
                                  child: Card(
                                      child: SizedBox(
                                    width: Get.width * .45,
                                    height: 120,
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Center(
                                          child: Text(
                                        "${controller.quiz3[controller.currentIndex.value].choices[1]}",
                                        style: Theme.of(context)
                                            .textTheme
                                            .titleLarge,
                                      )),
                                    ),
                                  )),
                                ),
                              ),
                            ],
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Obx(() => InkWell(
                                    onTap: () async {
                                      int randomNumber =
                                          controller.random.nextInt(12) + 1;
                                      if (controller.quizCheckAnswer(
                                          "${controller.quiz3[controller.currentIndex.value].choices[2]}",
                                          controller.quiz3[
                                              controller.currentIndex.value])) {
                                        await controller.player.play(AssetSource(
                                            'audio/correct${randomNumber}.mp3'));
                                        controller.list[controller
                                            .currentIndex.value] = Answer.right;
                                        //    controller.increaseScore();
                                      } else {
                                        await controller.player.play(
                                            AssetSource('audio/wrong.mp3'));
                                        controller.list[controller
                                            .currentIndex.value] = Answer.wrong;
                                      }
                                    },
                                    child: Card(
                                        child: SizedBox(
                                      width: Get.width * .45,
                                      height: 120,
                                      child: Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Center(
                                            child: Text(
                                          "${controller.quiz3[controller.currentIndex.value].choices[2]}",
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleLarge,
                                        )),
                                      ),
                                    )),
                                  )),
                              Obx(() => InkWell(
                                    onTap: () async {
                                      int randomNumber =
                                          controller.random.nextInt(12) + 1;
                                      if (controller.quizCheckAnswer(
                                          "${controller.quiz3[controller.currentIndex.value].choices[3]}",
                                          controller.quiz3[
                                              controller.currentIndex.value])) {
                                        await controller.player.play(AssetSource(
                                            'audio/correct${randomNumber}.mp3'));
                                        controller.list[controller
                                            .currentIndex.value] = Answer.right;
                                        //  controller.increaseScore();
                                      } else {
                                        await controller.player.play(
                                            AssetSource('audio/wrong.mp3'));
                                        controller.list[controller
                                            .currentIndex.value] = Answer.wrong;
                                      }
                                    },
                                    child: Card(
                                        child: SizedBox(
                                      width: Get.width * .45,
                                      height: 120,
                                      child: Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Center(
                                            child: Text(
                                          "${controller.quiz3[controller.currentIndex.value].choices[3]}",
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleLarge,
                                        )),
                                      ),
                                    )),
                                  )),
                            ],
                          )
                        ],
                      )),
            Spacer(),
            Obx(() => controller.quiz3.isEmpty
                ? Container()
                : controller.currentIndex.value == controller.quiz3.length
                    ? Container()
                    : controller.list[controller.currentIndex.value] ==
                            Answer.pending
                        ? Container()
                        : Container(
                            color: controller
                                        .list[controller.currentIndex.value] ==
                                    Answer.pending
                                ? null
                                : controller.list[
                                            controller.currentIndex.value] ==
                                        Answer.right
                                    ? Colors.greenAccent[400]
                                    : Colors.redAccent[200],
                            width: Get.width,
                            child: Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(left: 15),
                                  child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                        controller.list[controller
                                                    .currentIndex.value] ==
                                                Answer.pending
                                            ? ''
                                            : controller.list[controller
                                                        .currentIndex.value] ==
                                                    Answer.right
                                                ? "well done"
                                                : "Try again",
                                        style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white)),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(left: 15),
                                  child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      controller.list[controller
                                                  .currentIndex.value] ==
                                              Answer.pending
                                          ? ''
                                          : controller.list[controller
                                                      .currentIndex.value] ==
                                                  Answer.right
                                              ? ""
                                              : "Correct Answer: ${controller.quiz3[controller.currentIndex.value].answer}",
                                      style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white),
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(bottom: 15),
                                  child: ElevatedButton(
                                      style: ElevatedButton.styleFrom(
                                          shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(8)),
                                          backgroundColor: controller.list[
                                                      controller.currentIndex
                                                          .value] ==
                                                  Answer.right
                                              ? Colors.green
                                              : Colors.red,
                                          minimumSize:
                                              Size(Get.width * .95, 50)),
                                      onPressed: () async {
                                        controller.nextQuestion();

                                        if (controller.currentIndex.value >=
                                            controller.quiz3.length) {
                                          if (await controller.inAppReview
                                              .isAvailable()) {
                                            controller.inAppReview
                                                .requestReview();
                                          }
                                          quizCompleteDialog(controller.list
                                              .where((p0) => p0 == Answer.right)
                                              .length);
                                        }
                                      },
                                      child: Text("Continue",
                                          style:
                                              TextStyle(color: Colors.white))),
                                ),
                              ],
                            ),
                          )),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                            side: BorderSide(color: Colors.teal)),
                        minimumSize: Size(Get.width * .45, 50)),
                    onPressed: () {
                      controller.previousQuestion();
                    },
                    child: Text("Previous Page",
                        style: TextStyle(color: Colors.teal)),
                  ),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8)),
                        backgroundColor: Colors.teal,
                        minimumSize: Size(Get.width * .45, 50)),
                    onPressed: () {
                      controller.getNextQuestion();
                    },
                    child: Text("Next Page",
                        style: TextStyle(color: Colors.white)),
                  ),
                ],
              ),
            )
          ],
        ));
    throw UnimplementedError();
  }

  void quizCompleteDialog(int score) {
    Get.dialog(
      barrierDismissible: false,
      AlertDialog(
        title: Text('Your Score: $score/${controller.list.length}'),
        content: SizedBox(
          height: Get.height * .3,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "Congratulations 👏",
                style: titleStyle,
              ),
              Text(
                "You completed the quiz.",
                style: subTitleStyle,
              ),
              SizedBox(
                height: AppValues.padding,
              ),
              Text(
                controller.list.where((p0) => p0 == Answer.wrong).length > 1
                    ? "Take the quiz again to correct your mistake."
                    : "",
                style: subTitleStyle,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              controller.updateScore("$score");
            },
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  closeDialog() {
    return Get.dialog(
      AlertDialog(
        title: const Text('Quiz'),
        content: const Text('Are you sure you want to quit the quiz?'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text('No'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.back();
            },
            child: Text('yes'),
          ),
        ],
      ),
    );
  }
}
