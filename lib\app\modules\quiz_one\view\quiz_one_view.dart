import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/values/app_values.dart';
import 'package:news_lexica_app/app/core/values/text_styles.dart';

import '../controller/quiz_one_controller.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class QuizOneView extends BaseView<QuizOneController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) => null;

  @override
  Widget body(BuildContext context) {
    return PopScope(
      onPopInvoked: (didPop) {
        if (!didPop) closeDialog();
      },
      canPop: false,
      child: Column(
        children: [
          SizedBox(height: AppValues.extraLargePadding),

          // Header Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                onPressed: closeDialog,
                icon: Icon(Icons.close, size: 29, color: Colors.grey[800]),
              ),
              Obx(
                () => Text(
                  "Score ${controller.list.where((p) => p == Answer.right).length}/${controller.quiz3.length}",
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.teal[700],
                  ),
                ),
              ),
              SizedBox(width: 48), // to balance close icon space
            ],
          ),

          // Question Instruction
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text(
              "নিচের words এর সঠিক অর্থ টেপ করুন:",
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
          ),

          SizedBox(height: AppValues.largePadding),

          // Question Card with animation
          Obx(
            () =>
                controller.quiz3.isEmpty ||
                        controller.currentIndex.value == controller.quiz3.length
                    ? Container()
                    : AnimatedSwitcher(
                      duration: Duration(milliseconds: 300),
                      transitionBuilder:
                          (child, anim) => FadeTransition(
                            opacity: anim,
                            child: SlideTransition(
                              position: anim.drive(
                                Tween<Offset>(
                                  begin: Offset(0.0, 0.2),
                                  end: Offset.zero,
                                ).chain(CurveTween(curve: Curves.easeOut)),
                              ),
                              child: child,
                            ),
                          ),
                      child: Padding(
                        key: ValueKey(controller.currentIndex.value),
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: Card(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          elevation: 4,
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              gradient: LinearGradient(
                                colors: [Colors.teal.shade50, Colors.white],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                            ),
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppValues.largePadding,
                              vertical: AppValues.padding,
                            ),
                            child: Text(
                              "${controller.quiz3[controller.currentIndex.value].question}",
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(fontWeight: FontWeight.w600),
                            ),
                          ),
                        ),
                      ),
                    ),
          ),

          SizedBox(height: AppValues.largePadding),

          // Answer Buttons
          _buildAnswerOptions(context),

          Spacer(),

          // Feedback Section with Animated Color
          _buildFeedbackSection(context),

          // Navigation Buttons
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _modernButton(
                  label: "Previous Page",
                  onTap: controller.previousQuestion,
                  isPrimary: false,
                ),
                _modernButton(
                  label: "Next Page",
                  onTap: controller.getNextQuestion,
                  isPrimary: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnswerOptions(BuildContext context) {
    return Obx(() {
      if (controller.quiz3.isEmpty ||
          controller.currentIndex.value == controller.quiz3.length) {
        return Container();
      }
      return Column(
        children: [
          for (int i = 0; i < 2; i++)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                for (int j = 0; j < 2; j++)
                  _answerCard(
                    controller
                        .quiz3[controller.currentIndex.value]
                        .choices[i * 2 + j],
                    () => _handleAnswer(i * 2 + j),
                  ),
              ],
            ),
        ],
      );
    });
  }

  Widget _answerCard(String choice, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Card(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        elevation: 3,
        child: SizedBox(
          width: Get.width * .45,
          height: 120,
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                choice,
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeedbackSection(BuildContext context) {
    return Obx(() {
      if (controller.quiz3.isEmpty ||
          controller.currentIndex.value == controller.quiz3.length ||
          controller.list[controller.currentIndex.value] == Answer.pending) {
        return Container();
      }
      bool isRight =
          controller.list[controller.currentIndex.value] == Answer.right;

      return AnimatedContainer(
        duration: Duration(milliseconds: 400),
        curve: Curves.easeInOut,
        color: isRight ? Colors.greenAccent[400] : Colors.redAccent[200],
        width: Get.width,
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  isRight ? "Well done" : "Try again",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            if (!isRight)
              Padding(
                padding: const EdgeInsets.only(left: 15, bottom: 8),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    "Correct Answer: ${controller.quiz3[controller.currentIndex.value].answer}",
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            Padding(
              padding: const EdgeInsets.only(bottom: 15),
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  backgroundColor: isRight ? Colors.green : Colors.red,
                  minimumSize: Size(Get.width * .95, 50),
                ),
                onPressed: () async {
                  controller.nextQuestion();
                  if (controller.currentIndex.value >=
                      controller.quiz3.length) {
                    if (await controller.inAppReview.isAvailable()) {
                      controller.inAppReview.requestReview();
                    }
                    quizCompleteDialog(
                      controller.list.where((p0) => p0 == Answer.right).length,
                    );
                  }
                },
                child: Text("Continue", style: TextStyle(color: Colors.white)),
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _modernButton({
    required String label,
    required VoidCallback onTap,
    bool isPrimary = true,
  }) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: isPrimary ? BorderSide.none : BorderSide(color: Colors.teal),
        ),
        backgroundColor: isPrimary ? Colors.teal : Colors.transparent,
        minimumSize: Size(Get.width * .45, 50),
      ),
      onPressed: onTap,
      child: Text(
        label,
        style: TextStyle(color: isPrimary ? Colors.white : Colors.teal),
      ),
    );
  }

  Future<void> _handleAnswer(int choiceIndex) async {
    int randomNumber = controller.random.nextInt(12) + 1;
    bool isCorrect = controller.quizCheckAnswer(
      "${controller.quiz3[controller.currentIndex.value].choices[choiceIndex]}",
      controller.quiz3[controller.currentIndex.value],
    );

    if (isCorrect) {
      await controller.player.play(
        AssetSource('audio/correct$randomNumber.mp3'),
      );
      controller.list[controller.currentIndex.value] = Answer.right;
    } else {
      await controller.player.play(AssetSource('audio/wrong.mp3'));
      controller.list[controller.currentIndex.value] = Answer.wrong;
    }
  }

  // Your dialogs remain the same

  void quizCompleteDialog(int score) {
    Get.dialog(
      barrierDismissible: false,
      AlertDialog(
        title: Text('Your Score: $score/${controller.list.length}'),
        content: SizedBox(
          height: Get.height * .3,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text("Congratulations 👏", style: titleStyle),
              Text("You completed the quiz.", style: subTitleStyle),
              SizedBox(height: AppValues.padding),
              Text(
                controller.list.where((p0) => p0 == Answer.wrong).length > 1
                    ? "Take the quiz again to correct your mistake."
                    : "",
                style: subTitleStyle,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              controller.updateScore("$score");
            },
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  closeDialog() {
    return Get.dialog(
      AlertDialog(
        title: const Text('Quiz'),
        content: const Text('Are you sure you want to quit the quiz?'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text('No'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.back();
            },
            child: Text('yes'),
          ),
        ],
      ),
    );
  }
}
