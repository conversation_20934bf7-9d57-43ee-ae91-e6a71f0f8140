
class Lesson {
  String picture;
  String title;
  String paragraph;
  String audio;
  Map<String, dynamic> quiz1;
  List<QuizTwo> quiz2;
  List<Map<String, dynamic>> quiz3;
  num contentType;
  String category;
  int lessonNumber;
  int part;
  String id;
  String? lessonTitle;
  String? pressName;
  String? publishDate;

  Lesson({
    required this.picture,
    required this.title,
    required this.paragraph,
    required this.audio,
    required this.quiz1,
    required this.quiz2,
    required this.quiz3,
    required this.contentType,
    required this.category,
    required this.lessonNumber,
    required this.part,
    required this.id,
    required this.lessonTitle,
    required this.pressName,
    required this.publishDate,
  });


  // Convert Lesson object to JSON
  Map<String, dynamic> toJson() {
    List<Map<String, dynamic>> quiz2Json = quiz2.map((quiz) => quiz.toJson()).toList();

    return {
      'picture': picture,
      'title': title,
      'paragraph': paragraph,
      'audio': audio,
      'quizTwoData': quiz2Json,
      'contentType': contentType,
      'category': category,
      'lessonNumber': lessonNumber,
      'part': part,
      'lessonTitle': lessonTitle,
      'pressName': pressName,
      'publishDate': publishDate,
    };
  }

  // Create a Lesson object from JSON
  factory Lesson.fromJson(Map<String, dynamic> json) => Lesson(
    picture: json['picture'],
    title: json['title'],
    paragraph: json['paragraph'],
    audio: json['audio'],
    quiz1: json['quiz1'],
    quiz2: json['quiz2'],
    quiz3: List<Map<String, dynamic>>.from(json['quiz3']),
    contentType: json['contentType'],
    category: json['category'],
    lessonNumber: json['lessonNumber'],
    part: json['part'],
    id: json['id'],
    lessonTitle: json['lessonTitle'],
    pressName: json['pressName'],
    publishDate: json['publishDate'],
  );





  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'picture': picture,
      'title': title,
      'paragraph': paragraph,
      'audio': audio,
      'quiz1': quiz1.toString(),
      'quiz2': quiz2.toString(),
      'quiz3': quiz3.toString(),
      'contentType': contentType,
      'category': category,
      'lessonNumber': lessonNumber,
      'part': part,
    };
  }

  factory Lesson.fromMap(Map<String, dynamic> map) {
    return Lesson(
      id: map['id'],
      picture: map['picture'],
      title: map['title'],
      paragraph: map['paragraph'],
      audio: map['audio'],
      quiz1: map['quiz1'],
      quiz2: map['quiz2'],
      quiz3: map['quiz3'],
      contentType: map['contentType'],
      category: map['category'],
      lessonNumber: map['lessonNumber'],
      part: map['part'],
      lessonTitle: map?["lessonTitle"]??"",
      pressName: map?["pressName"]??"",
      publishDate:map?["publishDate"]??"",
    );
  }
}


class QuizTwo{
  String question;
  String answer;
  QuizTwo({required this.answer,required this.question});

  Map<String, dynamic> toJson() => {
    'question': question,
    'answer': answer,
  };
  factory QuizTwo.fromJson(Map<String, dynamic> json) {
    return QuizTwo(
      question: json['question'] ?? '',
      answer: json['answer'] ?? '',
    );
  }
}