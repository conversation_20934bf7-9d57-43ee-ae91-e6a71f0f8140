import 'dart:async';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import '../../../data/repository/pref_repository.dart';
import '../../../routes/app_pages.dart';

class SplashController extends BaseController {
  final PrefRepository _pref = Get.find(tag: (PrefRepository).toString());

  Future<String> getToken(String key) {
    return _pref.getString(key);
  }

  Future<void> _checkForToken() async {
    String token =
        await getToken('token'); // Replace 'token' with your actual key

    if (token != null && token.isNotEmpty) {
      _navigateMainView();
    } else {
      _navigateUserSignIn();
    }
  }

  void _navigateMainView() {
    Get.offAllNamed(
      Routes.MAIN,
    );
  }

  void _navigateUserSignIn() {
    Get.offAllNamed(
      Routes.USER_REGISTER,
    );
  }

  @override
  void onInit() {
    Timer(Duration(seconds: 2), () => _checkForToken());

    super.onInit();
  }
}
