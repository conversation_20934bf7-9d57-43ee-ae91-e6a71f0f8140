import 'dart:ffi';

import 'package:audioplayers/audioplayers.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';

import '../../home/<USER>/lesson_ui_data.dart';

class LessonReadingController extends BaseController {
  final Rx<LessonUiData> _lessonUiDataController = Rx(LessonUiData());
  LessonUiData get lessonUiData => _lessonUiDataController.value;

  var audionSrc = '';
  final audioPlayer = AudioPlayer();
  var audio = false.obs;
  var loading = false.obs;
  var duration = Duration().obs;
  var position = Duration().obs;
  var isPlaying = false.obs;
  double progressValue = 0.5; // Initial progress value (between 0.0 and 1.0)
  double volumeValue = 0.7; // Initial volume value (between 0.0 and 1.0)
  RxDouble sliderValue = 0.0.obs;
  Future<void> togglePlayPause() async {
    loading.value = true;
    try {
      if (audio.value) {
        await audioPlayer.pause().then((value) {
          loading.value = false;
          audio.value = false;
          isPlaying.value = false;
        });
      } else {
        await audioPlayer.play(UrlSource(audionSrc)).whenComplete(() {
          isPlaying.value = true;
          audio.value = true;
          loading.value = false;
        });
      }
    } catch (e) {}
  }

  void forward10Seconds() {
    Duration newPosition = position.value + Duration(seconds: 10);
    if (newPosition < duration.value) {
      audioPlayer.seek(newPosition);
    } else {
      audioPlayer.seek(
        duration.value,
      ); // If the new position exceeds the duration, go to the end.
    }
  }

  void backward10Seconds() {
    Duration newPosition = position.value - Duration(seconds: 10);
    if (newPosition > Duration(seconds: 0)) {
      audioPlayer.seek(newPosition);
    } else {
      audioPlayer.seek(
        Duration(seconds: 0),
      ); // If the new position is negative, go to the beginning.
    }
  }

  @override
  void onInit() {
    var dataModel = Get.arguments;
    if (dataModel is LessonUiData) {
      _lessonUiDataController(dataModel);
      audionSrc = "${dataModel.audio}";
    }
    audioPlayer.onDurationChanged.listen((dur) {
      duration.value = dur;
    });

    audioPlayer.onPositionChanged.listen((pos) {
      position.value = pos;
      sliderValue.value = pos.inSeconds.toDouble();
    });
    super.onInit();
  }

  @override
  void onClose() {
    audioPlayer.dispose();
    super.onClose();
  }
}
