import 'package:flutter/material.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/modules/news/news_reding/controller/new_reading_controller.dart';
import 'package:webview_flutter/webview_flutter.dart';

class NewReadingView extends BaseView<NewReadingController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return WebViewWidget(controller: controller.webViewController);
    throw UnimplementedError();
  }
}
