import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import '/app/core/values/app_colors.dart';
import '/app/core/values/app_values.dart';
import '/app/core/widget/elevated_container.dart';

class Loading extends StatefulWidget {
  const Loading({Key? key}) : super(key: key);

  @override
  State<Loading> createState() => _LoadingState();
}

class _LoadingState extends State<Loading> with TickerProviderStateMixin {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: ElevatedContainer(
        padding: EdgeInsets.all(AppValues.margin),
        child: Container(
          height: AppValues.iconExtraLargerSize,
          width: AppValues.iconExtraLargerSize,
          child: SpinKitRotatingCircle(
            color: AppColors.colorAccent,
            size: 50.0,
            controller: AnimationController(
              vsync: this,
              duration: const Duration(milliseconds: 1200),
            ),
          ),
        ),
      ),
    );
  }
}
