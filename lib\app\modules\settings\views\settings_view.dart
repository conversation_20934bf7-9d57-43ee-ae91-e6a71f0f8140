import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/utils/api.dart';
import 'package:news_lexica_app/app/core/widget/elevated_container.dart';
import '../../../../theme.dart';
import '../../../routes/app_pages.dart';
import '/app/core/base/base_view.dart';
import '/app/modules/settings/controllers/settings_controller.dart';

class SettingsView extends BaseView<SettingsController> {
  SettingsView() {
    controller.getUserData();
  }

  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
  }

  @override
  Widget body(BuildContext context) {
    return CustomScrollView(
      physics: const BouncingScrollPhysics(),
      slivers: [
        // Profile Header Section
        SliverToBoxAdapter(
          child: Container(
            margin: const EdgeInsets.fromLTRB(16, 8, 16, 24),
            child: _buildProfileCard(context),
          ),
        ),

        // Settings Sections
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionTitle(context, 'Account'),
                const SizedBox(height: 12),
                _buildModernCard(
                  context,
                  children: [
                    _buildModernListTile(
                      context,
                      icon: Icons.person_outline_rounded,
                      title: 'User Profile',
                      subtitle: 'Manage your personal information',
                      onTap: _navigateUserProfilePage,
                      showArrow: true,
                    ),
                    const Divider(height: 1, indent: 56),
                    _buildModernListTile(
                      context,
                      icon: Icons.subscriptions_outlined,
                      title: 'Subscription',
                      subtitle: 'Manage your subscription plan',
                      onTap: () => Get.toNamed(Routes.USER_SUBSCRBE),
                      showArrow: true,
                    ),
                  ],
                ),

                const SizedBox(height: 24),
                _buildSectionTitle(context, 'Preferences'),
                const SizedBox(height: 12),
                _buildModernCard(
                  context,
                  children: [
                    Obx(
                      () => _buildModernListTile(
                        context,
                        icon: Icons.notifications_outlined,
                        title: 'Notifications',
                        subtitle: 'Push notifications and alerts',
                        trailing: Transform.scale(
                          scale: 0.8,
                          child: Switch.adaptive(
                            value: controller.isNotificationEnabled.value,
                            onChanged: (bool value) {
                              controller.toggleNotification(value);
                            },
                            activeColor: Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                    ),
                    const Divider(height: 1, indent: 56),
                    ObxValue(
                      (data) => _buildModernListTile(
                        context,
                        icon:
                            isLightTheme.value
                                ? Icons.light_mode_outlined
                                : Icons.dark_mode_outlined,
                        title: isLightTheme.value ? 'Light Mode' : 'Dark Mode',
                        subtitle: 'Choose your preferred theme',
                        trailing: Transform.scale(
                          scale: 0.8,
                          child: Switch.adaptive(
                            value: isLightTheme.value,
                            onChanged: (bool value) {
                              isLightTheme.value = value;
                              Get.changeThemeMode(
                                isLightTheme.value
                                    ? ThemeMode.light
                                    : ThemeMode.dark,
                              );
                              saveThemeStatus();
                            },
                            activeColor: Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                      false.obs,
                    ),
                    const Divider(height: 1, indent: 56),
                    _buildModernListTile(
                      context,
                      icon: Icons.format_size_outlined,
                      title: 'Font Size',
                      subtitle: 'Adjust text size for better readability',
                      onTap:
                          () => showFontSizeDialog(
                            context,
                            controller.fontSizeController,
                          ),
                      showArrow: true,
                    ),
                  ],
                ),

                const SizedBox(height: 24),
                _buildSectionTitle(context, 'Support & Feedback'),
                const SizedBox(height: 12),
                _buildModernCard(
                  context,
                  children: [
                    _buildModernListTile(
                      context,
                      icon: Icons.chat_outlined,
                      title: 'Contact Support',
                      subtitle: 'Get help from our support team',
                      onTap:
                          () => Api().launchWhatsApp(
                            phoneNumber: "+8801860507913",
                            message: 'I need help',
                          ),
                      showArrow: true,
                    ),
                    const Divider(height: 1, indent: 56),
                    _buildModernListTile(
                      context,
                      icon: Icons.star_outline_rounded,
                      title: 'Rate Our App',
                      subtitle: 'Share your experience with others',
                      onTap:
                          () => Api().launchURL(
                            "https://play.google.com/store/apps/details?id=com.munir.newslexica&pli=1",
                          ),
                      showArrow: true,
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: List.generate(
                          5,
                          (index) =>
                              Icon(Icons.star, size: 16, color: Colors.amber),
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),
                _buildModernCard(
                  context,
                  children: [
                    _buildModernListTile(
                      context,
                      icon: Icons.logout_outlined,
                      title: 'Sign Out',
                      subtitle: 'Sign out from your account',
                      onTap: () => _showSignOutDialog(context),
                      showArrow: true,
                      iconColor: Colors.red,
                      textColor: Colors.red,
                    ),
                  ],
                ),

                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfileCard(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).primaryColor.withOpacity(0.8),
            Theme.of(context).primaryColor,
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: _navigateUserProfilePage,
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                Obx(
                  () => Hero(
                    tag: 'profile_avatar',
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 4),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: CircleAvatar(
                        radius: 50,
                        backgroundColor: Colors.white,
                        backgroundImage:
                            controller.userUiData.photo == null ||
                                    controller.userUiData.photo == ""
                                ? null
                                : NetworkImage(
                                  '${controller.userUiData.photo}',
                                ),
                        child:
                            controller.userUiData.photo == null ||
                                    controller.userUiData.photo == ""
                                ? Icon(
                                  Icons.person_rounded,
                                  size: 50,
                                  color: Theme.of(context).primaryColor,
                                )
                                : null,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Obx(
                  () =>
                      controller.userUiData.name == null
                          ? Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 10,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(25),
                              border: Border.all(
                                color: Colors.white.withOpacity(0.3),
                              ),
                            ),
                            child: Text(
                              "Complete Your Profile",
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                          )
                          : Text(
                            "${controller.userUiData.name}",
                            style: Theme.of(
                              context,
                            ).textTheme.headlineSmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.edit_outlined, color: Colors.white, size: 16),
                      const SizedBox(width: 6),
                      Text(
                        "Edit Profile",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(left: 4),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  Widget _buildModernCard(
    BuildContext context, {
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(children: children),
    );
  }

  Widget _buildModernListTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    String? subtitle,
    VoidCallback? onTap,
    Widget? trailing,
    bool showArrow = false,
    Color? iconColor,
    Color? textColor,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: (iconColor ?? Theme.of(context).primaryColor)
                      .withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: iconColor ?? Theme.of(context).primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: textColor,
                      ),
                    ),
                    if (subtitle != null) ...[
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(
                            context,
                          ).textTheme.bodySmall?.color?.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              if (trailing != null)
                trailing
              else if (showArrow)
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  size: 16,
                  color: Theme.of(
                    context,
                  ).textTheme.bodySmall?.color?.withOpacity(0.5),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSignOutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Text(
              'Sign Out',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            content: Text('Are you sure you want to sign out of your account?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  Get.offAllNamed(Routes.USER_REGISTER);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text('Sign Out'),
              ),
            ],
          ),
    );
  }

  showFontSizeDialog(
    BuildContext context,
    FontSizeController fontSizeController,
  ) {
    return showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Font Size',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Choose your preferred font size for better readability.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(
                      context,
                    ).textTheme.bodyMedium?.color?.withOpacity(0.7),
                  ),
                ),
                const SizedBox(height: 24),
                _buildFontSizeOption(
                  context,
                  'Small',
                  FontSizes.small,
                  fontSizeController,
                ),
                const SizedBox(height: 8),
                _buildFontSizeOption(
                  context,
                  'Medium',
                  FontSizes.medium,
                  fontSizeController,
                ),
                const SizedBox(height: 8),
                _buildFontSizeOption(
                  context,
                  'Large',
                  FontSizes.large,
                  fontSizeController,
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text('Cancel'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFontSizeOption(
    BuildContext context,
    String label,
    FontSizes size,
    FontSizeController controller,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          controller.changeFontSize(size);
          Navigator.pop(context);
        },
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).dividerColor),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
          ),
        ),
      ),
    );
  }

  _navigateUserProfilePage() {
    Get.toNamed(Routes.USER_PROFILE);
  }
}
