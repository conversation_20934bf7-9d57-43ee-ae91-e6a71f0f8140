import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/utils/api.dart';
import 'package:news_lexica_app/app/core/widget/elevated_container.dart';
import '../../../../theme.dart';
import '../../../routes/app_pages.dart';
import '/app/core/base/base_view.dart';
import '/app/modules/settings/controllers/settings_controller.dart';

class SettingsView extends BaseView<SettingsController> {
  SettingsView() {
    controller.getUserData();
  }
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
  }

  @override
  Widget body(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: ListView(
        children: [
          SizedBox(
            height: 20,
          ),
          Obx(() => InkWell(
                onTap: _navigateUserProfilePage,
                child: ElevatedContainer(
                  bgColor: Theme.of(context).canvasColor,
                  child: Column(
                    children: [
                      SizedBox(
                        height: 20,
                      ),
                      controller.userUiData.photo == null
                          ? CircleAvatar(
                              backgroundColor: Colors.tealAccent,
                              radius: 50,
                              child: Icon(
                                Icons.person,
                                size: 50,
                              ),
                            )
                          : CircleAvatar(
                              backgroundImage: controller.userUiData.photo == ""
                                  ? const AssetImage("assets/user.png")
                                      as ImageProvider
                                  : NetworkImage(
                                      '${controller.userUiData.photo}'),
                              radius: 50,
                            ),
                      SizedBox(
                        height: 10,
                      ),
                      controller.userUiData.name == null
                          ? TextButton(
                              onPressed: () {
                                // Get.to(()=>ProfileDetailsPage());
                              },
                              child: Text("Complete Your Profile"))
                          : Text(
                              "${controller.userUiData.name}",
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                      SizedBox(
                        height: 20,
                      ),
                    ],
                  ),
                ),
              )),
          SizedBox(
            height: 20,
          ),
          ElevatedContainer(
            bgColor: Theme.of(context).canvasColor,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: [
                  ListTile(
                    onTap: _navigateUserProfilePage,
                    leading: Icon(Icons.person),
                    title: Text("User Profile", style: TextStyle()),
                    trailing: Icon(Icons.keyboard_arrow_right),
                    // trailing:,
                  ),
                  ListTile(
                    onTap: () => Get.toNamed(Routes.USER_SUBSCRBE),
                    leading: Icon(Icons.subscriptions),
                    title: Text("Subscription", style: TextStyle()),
                    trailing: Icon(Icons.keyboard_arrow_right),
                    // trailing:,
                  ),
                  ListTile(
                    onTap: () => Get.toNamed(Routes.USER_SUBSCRBE),
                    leading: Icon(
                      Icons.notifications,
                    ),
                    title: Text("Notification", style: TextStyle()),
                    trailing: Obx(() => Switch(
                          inactiveThumbColor: Colors.white,
                          inactiveTrackColor: Colors.blueAccent,
                          value: controller.isNotificationEnabled.value,
                          onChanged: (bool value) {
                            controller.toggleNotification(value);
                          },
                        )),
                  ),
                  Divider(),
                  ListTile(
                    leading: ObxValue(
                        (data) => isLightTheme.value
                            ? Icon(Icons.light_mode)
                            : Icon(Icons.dark_mode),
                        false.obs),
                    title: ObxValue(
                        (data) => Text(
                            isLightTheme.value ? "Light Mode" : "Dark Mode",
                            style: TextStyle()),
                        false.obs),
                    trailing: ObxValue(
                      (data) => Switch(
                        inactiveThumbColor: Colors.white,
                        inactiveTrackColor: Colors.blueAccent,
                        value: isLightTheme.value,
                        onChanged: (bool value) {
                          isLightTheme.value = value;
                          Get.changeThemeMode(
                            isLightTheme.value
                                ? ThemeMode.light
                                : ThemeMode.dark,
                          );
                          saveThemeStatus();
                        },
                      ),
                      false.obs,
                    ),
                  ),
                  Divider(),
                  ListTile(
                    onTap: () => Api().launchWhatsApp(
                        phoneNumber: "+8801860507913", message: 'I need help'),
                    leading: Icon(Icons.chat),
                    title: Text("Contact the support team", style: TextStyle()),
                    trailing: Icon(Icons.keyboard_arrow_right),
                  ),
                  Divider(),
                  ListTile(
                    onTap: () {
                      showFontSizeDialog(
                          context, controller.fontSizeController);
                    },
                    leading: Icon(Icons.format_size),
                    title: Text("Increase Font Size", style: TextStyle()),
                    trailing: Icon(Icons.keyboard_arrow_right),
                  ),
                  Divider(),
                  ListTile(
                    onTap: () {
                      Api().launchURL(
                          "https://play.google.com/store/apps/details?id=com.munir.newslexica&pli=1");
                    },
                    leading: Icon(Icons.reviews),
                    title: Text("Rate Us ⭐⭐⭐⭐⭐", style: TextStyle()),
                    trailing: Icon(Icons.keyboard_arrow_right),
                  ),
                  Divider(),
                  ListTile(
                    onTap: () {
                      Get.offAllNamed(Routes.USER_REGISTER);
                    },
                    leading: Icon(Icons.logout),
                    title: Text("Sign Out", style: TextStyle()),
                    trailing: Icon(Icons.keyboard_arrow_right),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _getHorizontalDivider() {
    return const Divider(height: 1);
  }

  showFontSizeDialog(
      BuildContext context, FontSizeController fontSizeController) {
    return showDialog(
      context: context,
      builder: (context) {
        return SimpleDialog(
          title: Text('Select Font Size'),
          children: <Widget>[
            SimpleDialogOption(
              onPressed: () {
                // Change the font size to small
                fontSizeController.changeFontSize(FontSizes.small);
                Navigator.pop(context);
              },
              child: Text('Small'),
            ),
            SimpleDialogOption(
              onPressed: () {
                // Change the font size to medium
                fontSizeController.changeFontSize(FontSizes.medium);
                Navigator.pop(context);
              },
              child: Text('Medium'),
            ),
            SimpleDialogOption(
              onPressed: () {
                // Change the font size to large
                fontSizeController.changeFontSize(FontSizes.large);
                Navigator.pop(context);
              },
              child: Text('Large'),
            ),
          ],
        );
      },
    );
  }

  _navigateUserProfilePage() {
    Get.toNamed(Routes.USER_PROFILE);
  }
}
