import 'package:get/get.dart';
import 'package:news_lexica_app/app/data/repository/authentication_repository.dart';
import 'package:news_lexica_app/app/data/repository/authentication_repository_impl.dart';
import 'package:news_lexica_app/app/data/repository/lesson_repository.dart';
import 'package:news_lexica_app/app/data/repository/lesson_repository_impl.dart';
import 'package:news_lexica_app/app/data/repository/pref_repository_impl.dart';
import 'package:news_lexica_app/app/data/repository/user_repository.dart';
import 'package:news_lexica_app/app/data/repository/user_repository_impl.dart';

import '../data/repository/pref_repository.dart';

class RepositoryBindings implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut<AuthenticationRepository>(() => AuthenticationRepositoryImpl(),
        tag: (AuthenticationRepository).toString(), fenix: true);

    Get.lazyPut<LessonRepository>(() => LessonRepositoryImpl(),
        tag: (LessonRepository).toString(), fenix: true);

    Get.lazyPut<UserRepository>(() => UserRepositoryImpl(),
        tag: (UserRepository).toString(), fenix: true);

    Get.lazyPut<PrefRepository>(() => PrefRepositoryImpl(),
        tag: (PrefRepository).toString(), fenix: true);
  }
}
