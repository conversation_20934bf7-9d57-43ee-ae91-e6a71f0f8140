import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/widget/elevated_container.dart';

import '../../../../core/values/app_values.dart';
import '../../../../routes/app_pages.dart';
import '../model/reading_passage_model.dart';

class ReadingPassageCard extends StatelessWidget {
  const ReadingPassageCard({Key? key, required this.reading}) : super(key: key);

  final ReadingPassageModel reading;

  @override
  Widget build(BuildContext context) {
    return ElevatedContainer(
      bgColor: Theme.of(context).cardColor,
      child: Padding(
        padding: const EdgeInsets.all(AppValues.padding),
        child: ListTile(
          onTap: () {
            Get.toNamed(
              Routes.NEW_IELTS_READING_PASSAGE_DETAILS,
              arguments: reading.id,
            );
          },
          leading: Image.asset("assets/lesson_read.png"),
          title: Text(
            reading.title,
            style: Theme.of(context).textTheme.titleMedium,
          ),
          trailing: Icon<PERSON>utton(
            icon: Icon(Icons.chevron_right),
            onPressed: () {
              Get.toNamed(
                Routes.NEW_IELTS_READING_PASSAGE_DETAILS,
                arguments: reading.id,
              );
            },
          ),
        ),
      ),
    );
  }
}
