part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

abstract class Routes {
  Routes._();

  static const MAIN = _Paths.MAIN;
  static const HOME = _Paths.HOME;
  static const FAVORITE = _Paths.FAVORITE;
  static const SETTINGS = _Paths.SETTINGS;
  static const OTHER = _Paths.OTHER;
  static const PROJECT_DETAILS = _Paths.PROJECT_DETAILS;
  static const USER_LOGIN = _Paths.USER_LOGIN;
  static const USER_REGISTER = _Paths.USER_REGISTER;
  static const USER_FORGETPASS = _Paths.USER_FORGETPASS;
  static const USER_OTP_RGISTER = _Paths.USER_OTP_RGISTER;
  static const LESSON_DETAILS = _Paths.LESSON_DETAILS;
  static const LESSON_READING = _Paths.LESSON_READING;
  static const LESSON_QUIZ_ONE = _Paths.LESSON_QUIZ_ONE;
  static const LESSON_QUIZ_TWO = _Paths.LESSON_QUIZ_TWO;
  static const LESSON_QUIZ_THREE = _Paths.LESSON_QUIZ_THREE;
  static const LESSON_CATEGORY = _Paths.LESSON_CATEGORY;
  static const ALL_CATEGORY = _Paths.ALL_CATEGORY;
  static const LESSON_QUIZ_MISTAKE = _Paths.LESSON_QUIZ_MISTAKE;
  static const LESSON_QUIZ_DISCUSSION = _Paths.LESSON_QUIZ_DISCUSSION;
  static const USER_SUBSCRBE = _Paths.USER_SUBSCRBE;
  static const USER_PROFILE = _Paths.USER_PROFILE;
  static const LESSON_LIST_TWO = _Paths.LESSON_LIST_TWO;
  static const UPDDATE_PASSWORD = _Paths.UPDDATE_PASSWORD;
  static const INTRO_VIDEO = _Paths.INTRO_VIDEO;
  static const EXAM_QUIZ_LIST = _Paths.EXAM_QUIZ_LIST;
  static const EXAM_QUIZ_DETAILS = _Paths.EXAM_QUIZ_DETAILS;
  static const SPLASH_WELCOME = _Paths.SPLASH_WELCOME;
  static const COMPETATION_INFO = _Paths.COMPETATION_INFO;
  static const SERVER_OFF = _Paths.SERVER_OFF;
  static const GRE_QUIZ_LIST = _Paths.GRE_QUIZ_LIST;
  static const GRE_QUI_DETAILS = _Paths.GRE_QUI_DETAILS;
  static const TRANSLATION_WORDS = _Paths.TRANSLATION_WORDS;
  static const NEWS_PAPER_LIST = _Paths.NEWS_PAPER_LIST;
  static const NEWS_PAPER_READING = _Paths.NEWS_PAPER_READING;
  static const APP_HEADING_DETAILS = _Paths.APP_HEADING_DETAILS;
  static const NEW_IELTS_BOOK_LIST = _Paths.NEW_IELTS_BOOK_LIST;
  static const NEW_IELTS_READING_LIST = _Paths.NEW_IELTS_READING_LIST;
  static const NEW_IELTS_READING_PASSAGE_LIST =
      _Paths.NEW_IELTS_READING_PASSAGE_LIST;
  static const NEW_IELTS_READING_PASSAGE_DETAILS =
      _Paths.NEW_IELTS_READING_PASSAGE_DETAILS;
  static const IELTS_STORY_READING_PAGE = _Paths.IELTS_STORY_READING_PAGE;
  static const IELTS_NEW_QUIZ_ONE = _Paths.IELTS_NEW_QUIZ_ONE;
  static const IELTS_NEW_QUIZ_TWO = _Paths.IELTS_NEW_QUIZ_TWO;
  static const IELTS_NEW_QUIZ_EXMPLE = _Paths.IELTS_NEW_QUIZ_EXMPLE;
  static const FREE_LESSON_VIEW = _Paths.FREE_LESSON_VIEW;
}

abstract class _Paths {
  static const MAIN = '/main';
  static const HOME = '/home';
  static const FAVORITE = '/favorite';
  static const SETTINGS = '/settings';
  static const OTHER = '/other';
  static const PROJECT_DETAILS = '/project-details';
  static const USER_LOGIN = '/user-login';
  static const USER_REGISTER = '/user-register';
  static const USER_OTP_RGISTER = '/user-otp-register';
  static const USER_FORGETPASS = '/user-forget-pass';
  static const LESSON_DETAILS = '/lesson-details';
  static const LESSON_READING = '/lesson-reading';
  static const LESSON_QUIZ_ONE = '/lesson-one';
  static const LESSON_QUIZ_TWO = '/lesson-two';
  static const LESSON_QUIZ_THREE = '/lesson-three';
  static const LESSON_CATEGORY = '/lesson-category';
  static const ALL_CATEGORY = '/all-category';
  static const LESSON_QUIZ_MISTAKE = '/lesson-quiz-mistake';
  static const LESSON_QUIZ_DISCUSSION = '/lesson-quiz-discussion';
  static const USER_SUBSCRBE = '/user-subscribe';
  static const USER_PROFILE = '/user-profile';
  static const LESSON_LIST_TWO = '/lesson-list-two';
  static const UPDDATE_PASSWORD = '/update-password';
  static const INTRO_VIDEO = '/intro-video';
  static const EXAM_QUIZ_LIST = '/exam-quiz-list';
  static const EXAM_QUIZ_DETAILS = '/exam-quiz-details';
  static const SPLASH_WELCOME = '/splash-welcome';
  static const COMPETATION_INFO = '/competation-info';
  static const SERVER_OFF = '/server-off';
  static const GRE_QUIZ_LIST = '/gre-quiz-list';
  static const GRE_QUI_DETAILS = '/gre-quiz-details';
  static const TRANSLATION_WORDS = '/translation-words';
  static const NEWS_PAPER_LIST = '/news-paper-list';
  static const NEWS_PAPER_READING = '/news-paper-reading';
  static const APP_HEADING_DETAILS = '/app-heading-details';
  static const NEW_IELTS_BOOK_LIST = '/new-itels-book-list';
  static const NEW_IELTS_READING_LIST = '/new-itels-reading-list';
  static const NEW_IELTS_READING_PASSAGE_LIST =
      '/new-itels-reading-passage-list';
  static const NEW_IELTS_READING_PASSAGE_DETAILS =
      '/new-itels-reading-passage-details';
  static const IELTS_STORY_READING_PAGE = '/new-itels-story-reading-page';
  static const IELTS_NEW_QUIZ_ONE = '/new-itels-quiz-one-page';
  static const IELTS_NEW_QUIZ_TWO = '/new-itels-quiz-two-page';
  static const IELTS_NEW_QUIZ_EXMPLE = '/new-itels-quiz-example';
  static const FREE_LESSON_VIEW = '/free-lesson_vew';
}
