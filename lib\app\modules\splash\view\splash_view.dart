import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';

import '../controller/splash_controller.dart';

class SplashView extends BaseView<SplashController>{
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
   return  MaterialApp(
     home: Scaffold(
       backgroundColor: Color(0xff1c1b1b),

       body:  Center(
         child:
         Column(
           mainAxisAlignment: MainAxisAlignment.center,
           children: [
             Row(
               mainAxisAlignment: MainAxisAlignment.center,
               children: [
                 Image.asset(
                   'assets/lexica_top_icon.png',
                   height: 50,
                 ),
                 SizedBox(width: 5,),
                 Text('NEWS ', style: GoogleFonts.poppins(fontWeight: FontWeight.bold,color: Colors.white,fontSize: 34)),
                 Text('LEXICA', style: GoogleFonts.poppins(fontWeight: FontWeight.bold,color: Colors.green,fontSize: 34)),

               ],),

             SizedBox(height:10,),
             Padding(
               padding: const EdgeInsets.only(left: 10),
               child: Text("Your Vocabulary Trainer",style: Theme.of(context).textTheme.titleMedium!.copyWith(color: Colors.white),),
             )
           ],),
       ), //<- place where the image appears
     ),
   );
    throw UnimplementedError();
  }

}