class ScoreBoardDataResponse {
  List<Scores>? scores;
  int? totalItems;
  int? totalPages;
  int? currentPage;

  ScoreBoardDataResponse(
      {this.scores, this.totalItems, this.totalPages, this.currentPage});

  ScoreBoardDataResponse.fromJson(Map<String, dynamic> json) {
    if (json['scores'] != null) {
      scores = <Scores>[];
      json['scores'].forEach((v) {
        scores!.add(new Scores.fromJson(v));
      });
    }
    totalItems = json['totalItems'];
    totalPages = json['totalPages'];
    currentPage = json['currentPage'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.scores != null) {
      data['scores'] = this.scores!.map((v) => v.toJson()).toList();
    }
    data['totalItems'] = this.totalItems;
    data['totalPages'] = this.totalPages;
    data['currentPage'] = this.currentPage;
    return data;
  }
}

class Scores {
  int? id;
  int? score;
  int? userId;
  User? user;

  Scores({this.id, this.score, this.userId, this.user});

  Scores.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    score = json['score'];
    userId = json['userId'];
    user = json['user'] != null ? new User.fromJson(json['user']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['score'] = this.score;
    data['userId'] = this.userId;
    if (this.user != null) {
      data['user'] = this.user!.toJson();
    }
    return data;
  }
}

class User {
  int? id;
  String? name;
  Null? photo;
  String? password;
  String? phone;

  User({this.id, this.name, this.photo, this.password, this.phone});

  User.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    photo = json['photo'];
    password = json['password'];
    phone = json['phone'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['photo'] = this.photo;
    data['password'] = this.password;
    data['phone'] = this.phone;
    return data;
  }
}