class LessonSearchQueryParam{
  String? searchKeyWord;
  int? contentType;
  String? category;
  int? part;

  LessonSearchQueryParam({
    this.searchKeyWord,
    this.contentType ,
    this.category,
    this.part,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['searchKeyWord'] = searchKeyWord;
    data['contentType'] = contentType;
    data['category'] = category;
    data['part'] = part;

    return data;
  }
}