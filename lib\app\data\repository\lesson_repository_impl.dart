import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/model/lesson_discussion_body_prem.dart';
import 'package:news_lexica_app/app/data/model/book_response_data.dart';
import 'package:news_lexica_app/app/data/model/exam_quiz_list.dart';
import 'package:news_lexica_app/app/data/model/gre_quiz_list.dart';
import 'package:news_lexica_app/app/data/model/lesson_data.dart';
import 'package:news_lexica_app/app/data/model/lesson_data_details.dart';
import 'package:news_lexica_app/app/data/model/lesson_discussion_response.dart';
import 'package:news_lexica_app/app/data/model/promotion_data_response.dart';
import 'package:news_lexica_app/app/data/model/reading_passage_details.dart';
import 'package:news_lexica_app/app/data/model/reading_passage_response_data.dart';
import 'package:news_lexica_app/app/data/model/reading_response_data.dart';
import 'package:news_lexica_app/app/data/remote/lesson_remote_data_source.dart';

import '../../core/model/discussion_query_prem.dart';
import '../../core/model/lesson_search_query_parm.dart';
import 'lesson_repository.dart';

class LessonRepositoryImpl implements LessonRepository {
  final LessonRemoteDataSrc _remoteSource =
      Get.find(tag: (LessonRemoteDataSrc).toString());

  @override
  Future<LessonDetailsData> getLessonDataDetails(int lessonId) {
    return _remoteSource.getLessonDataDetails(lessonId);
    throw UnimplementedError();
  }

  @override
  Future<LessonData> getLessonList(
      LessonSearchQueryParam lessonSearchQueryParam) {
    return _remoteSource.getLessonList(lessonSearchQueryParam);
    throw UnimplementedError();
  }

  @override
  Future<ExamQuizResponseData> getExamQuizListByLesson(int lessonNumber) {
    return _remoteSource.getExamQuizListByLesson(lessonNumber);
    throw UnimplementedError();
  }

  @override
  Future<PromotionDataResponse> getAllPromotion() {
    return _remoteSource.getAllPromotion();
    throw UnimplementedError();
  }

  @override
  Future<GreQuizList> getGreQuizListByLesson(int lessonNumber) {
    return _remoteSource.getGreQuizListByLesson(lessonNumber);
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> addLessonDiscussion(
      LessonDiscussionBodyPrem lessonDiscussionBodyPrem) {
    return _remoteSource.addLessonDiscussion(lessonDiscussionBodyPrem);
    throw UnimplementedError();
  }

  @override
  Future<LessonDiscussionResponse> getAllLessonDiscussion(
      DiscussionQueryPrem discussionQueryPrem) {
    return _remoteSource.getAllLessonDiscussion(discussionQueryPrem);
    throw UnimplementedError();
  }

  @override
  Future<BookResponseData> getBookList() {
    return _remoteSource.getBookList();
    throw UnimplementedError();
  }

  @override
  Future<ReadingResponseData> getReadingListOfBook(int id) {
    return _remoteSource.getReadingListOfBook(id);
    throw UnimplementedError();
  }

  @override
  Future<ReadingPassageDetails> getReadingPassageDetails(int id) {
    return _remoteSource.getReadingPassageDetails(id);
    throw UnimplementedError();
  }

  @override
  Future<ReadingPassageResponseData> getReadingPassageOfReadingList(int id) {
    return _remoteSource.getReadingPassageOfReadingList(id);
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> voteDiscussion(String id, String voteType) {
    return _remoteSource.voteDiscussion(id, voteType);
    throw UnimplementedError();
  }
}
