import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import 'package:news_lexica_app/app/modules/news/news_list/model/news_list_data_model.dart';
import 'package:webview_flutter/webview_flutter.dart';

class NewReadingController extends BaseController {
  WebViewController webViewController = WebViewController();

  @override
  void onInit() {
    var model = Get.arguments;
    if (model is NewsListDataModel) {
      webViewController
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(
          NavigationDelegate(
            onProgress: (int progress) {
              // Update loading bar.
            },
            onPageStarted: (String url) {},
            onPageFinished: (String url) {},
            onHttpError: (HttpResponseError error) {},
            onWebResourceError: (WebResourceError error) {},
            onNavigationRequest: (NavigationRequest request) {
              if (request.url.startsWith(model.url)) {
                return NavigationDecision.prevent;
              }
              return NavigationDecision.navigate;
            },
          ),
        )
        ..loadRequest(Uri.parse(model.url));
    }
    super.onInit();
  }
}
