class ReadingPassageResponseData {
  List<PassageList>? passageList;

  ReadingPassageResponseData({this.passageList});

  ReadingPassageResponseData.fromJson(Map<String, dynamic> json) {
    if (json['passageList'] != null) {
      passageList = <PassageList>[];
      json['passageList'].forEach((v) {
        passageList!.add(new PassageList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.passageList != null) {
      data['passageList'] = this.passageList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class PassageList {
  int? id;
  String? name;
  int? ieltsRadingListId;
  String? createdAt;
  String? updatedAt;

  PassageList(
      {this.id,
      this.name,
      this.ieltsRadingListId,
      this.createdAt,
      this.updatedAt});

  PassageList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    ieltsRadingListId = json['ieltsRadingListId'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['ieltsRadingListId'] = this.ieltsRadingListId;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    return data;
  }
}
