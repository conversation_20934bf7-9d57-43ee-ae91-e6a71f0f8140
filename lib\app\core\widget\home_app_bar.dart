
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class HomeAppBar extends StatelessWidget implements PreferredSizeWidget {
  const HomeAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return  AppBar(
      backgroundColor: Color(0xff151312),
      title:   Padding(
        padding: const EdgeInsets.only(right: 20),
        child: Column(

          children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [

              Image.asset(
                'assets/lexica_top_icon.png',
                height: 25,
              ),
              SizedBox(width: 5,),
              Text('NEWS', style: GoogleFonts.poppins(fontWeight: FontWeight.bold, color: Color(0xff2734A1),)),
              SizedBox(width: 2,),
              Text('LEXICA', style: GoogleFonts.poppins(fontWeight: FontWeight.bold,color: Colors.green,)),

            ],),

          Text("Your Vocabulary Trainer",style: Theme.of(context).textTheme.titleSmall!.copyWith(color: Colors.white),)
        ],),
      ),
      centerTitle: true,
    );
  }

  @override
  Size get preferredSize => AppBar().preferredSize;
}
