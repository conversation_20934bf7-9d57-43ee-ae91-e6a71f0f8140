import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import 'package:news_lexica_app/app/data/model/reading_passage_details.dart';

import '../../../../data/repository/lesson_repository.dart';

class ReadingPassageDetailsController extends BaseController {
  final LessonRepository _repository =
      Get.find(tag: (LessonRepository).toString());

  var readingPassageDetails = ReadingPassageDetails().obs;
  getReadingPassageDetails(int id) {
    var readingPassageDetails = _repository.getReadingPassageDetails(id);
    callDataService(readingPassageDetails,
        onSuccess: _handleReadingPassageDetailsResponseSuccess,
        onError: _handleReadingPassageDetailsError);
  }

  _handleReadingPassageDetailsResponseSuccess(ReadingPassageDetails response) {
    readingPassageDetails.value = response;
  }

  _handleReadingPassageDetailsError(Exception exception) {}

  @override
  void onInit() {
    var args = Get.arguments;
    if (args is int) {
      getReadingPassageDetails(args);
    }
    super.onInit();
  }
}
