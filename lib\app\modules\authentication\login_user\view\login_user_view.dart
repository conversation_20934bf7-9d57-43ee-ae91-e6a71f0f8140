import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';

import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:news_lexica_app/app/core/values/app_colors.dart';
import 'package:news_lexica_app/app/modules/authentication/login_user/controller/login_user_controller.dart';
import 'package:news_lexica_app/app/routes/app_pages.dart';

import '../../../../core/base/base_view.dart';
import '../../../../core/values/app_values.dart';

class LoginUserView extends BaseView<LoginUserController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          SizedBox(height: AppValues.extraLargeSpacing),
          _topBannerCard(context),
          SizedBox(height: AppValues.largePadding),
          Form(
            key: formKey,
            child: Column(
              children: [
                _textField(
                  controller.phoneController,
                  'Phone Number',
                  '01XXXXXXXXXX',
                ),
                _textField(controller.passwordController, 'Password', '****'),
              ],
            ),
          ),

          Align(
            alignment: Alignment.centerRight,
            child: TextButton(
              onPressed: () => Get.toNamed(Routes.USER_FORGETPASS),
              child: Text("Forget Password ?"),
            ),
          ),
          SizedBox(height: 30),

          Padding(
            padding: const EdgeInsets.all(8.0),
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                minimumSize: Size(Get.height * .9, 50),
                backgroundColor: Colors.teal,
              ),
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  controller.signInUser();
                }
              },
              child: Text(
                "Login",
                style: Theme.of(
                  context,
                ).textTheme.titleLarge!.copyWith(color: Colors.white),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 10, bottom: 10),
            child: Text("Don't Have an account?"),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: ElevatedButton(
              onPressed: () => Get.back(),
              child: Text(
                "Register",
                style: Theme.of(context).textTheme.titleLarge,
              ),
              style: ElevatedButton.styleFrom(
                elevation: 0.0,
                minimumSize: Size(Get.height * .9, 50),
                backgroundColor: Colors.transparent,
                side: BorderSide(color: Colors.teal),
              ),
            ),
          ),
        ],
      ),
    );
    throw UnimplementedError();
  }

  Widget _textField(
    TextEditingController controller,
    String title,
    String hintText,
  ) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          TextFormField(
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter $title';
              }
              return null;
            },
            controller: controller,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderSide: BorderSide.none,
                borderRadius: BorderRadius.circular(8),
              ),
              filled: true,

              hintText: hintText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _topBannerCard(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset('assets/lexica_top_icon.png', height: 25),
            SizedBox(width: 5),
            Text(
              'NEWS',
              style: GoogleFonts.poppins(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Color(0xff2734A1),
              ),
            ),
            SizedBox(width: 2),
            Text(
              'LEXICA',
              style: GoogleFonts.poppins(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ],
        ),
        Text(
          "Your Vocabulary Trainer",
          style: Theme.of(context).textTheme.titleSmall!.copyWith(fontSize: 18),
        ),
      ],
    );
  }
}
