class LessonDiscussionBodyPrem {
  final String user;
  final String comment;
  final String lesson;
  final String? parentId;

  // Constructor
  LessonDiscussionBodyPrem({
    this.parentId,
    required this.user,
    required this.comment,
    required this.lesson,
  });

  // Method to convert an instance to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'user': user,
      'comment': comment,
      'lesson': lesson,
      'parentId': parentId,
    };
  }
}
