import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import 'package:news_lexica_app/app/core/model/forget_pass_otp_model.dart';

import '../../../../data/repository/authentication_repository.dart';
import '../../../../routes/app_pages.dart';

class ForgetPassController extends BaseController {
  final AuthenticationRepository _repository = Get.find(
    tag: (AuthenticationRepository).toString(),
  );
  var phoneController = TextEditingController();

  checkUserExist(String phone) {
    var checkUserService = _repository.checkUserExist(phone);
    callDataService(
      checkUserService,
      onSuccess: _handleCheckUserSuccess,
      onError: _handleErrorUser,
    );
  }

  _sendOtp() {
    var sendOtpService = _repository.sendOtp(phoneController.text);
    callDataService(
      sendOtpService,
      onSuccess: _handleOtpSendSuccess,
      onError: _handleOtpSendError,
    );
  }

  _handleCheckUserSuccess(Map<String, dynamic> response) {
    if (response['messsage']) {
      Get.toNamed(
        Routes.UPDDATE_PASSWORD,
        arguments: ForgetPassOtpModel(
          phone: phoneController.text,
          hashedOtp: '',
        ),
      );
    } else {
      Fluttertoast.showToast(msg: "User doesn't exist");
    }
  }

  _handleErrorUser(Exception exception) {
    Fluttertoast.showToast(msg: errorMessage);
  }

  _handleOtpSendSuccess(Map<String, dynamic> response) {}

  _handleOtpSendError(Exception exception) {
    Fluttertoast.showToast(msg: errorMessage);
  }
}
