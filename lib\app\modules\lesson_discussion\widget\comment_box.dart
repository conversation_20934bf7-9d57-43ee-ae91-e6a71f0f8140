import 'package:flutter/material.dart';
import 'package:news_lexica_app/app/core/values/app_colors.dart';

class CommentBox extends StatefulWidget {
  final Function(String) onSubmit;
  final TextEditingController controller;
  const CommentBox({Key? key, required this.onSubmit, required this.controller})
    : super(key: key);

  @override
  _CommentBoxState createState() => _CommentBoxState();
}

class _CommentBoxState extends State<CommentBox> {
  @override
  void dispose() {
    widget.controller.dispose();
    super.dispose();
  }

  void _submitComment() {
    if (widget.controller.text.isNotEmpty) {
      widget.onSubmit(widget.controller.text);
      widget.controller.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: widget.controller,
              decoration: InputDecoration(
                hintText: 'Write a comment...',
                border: OutlineInputBorder(
                  borderSide: BorderSide(color: AppColors.colorDark),
                  borderRadius: BorderRadius.circular(10),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: AppColors.colorDark),
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),
          SizedBox(width: 10),
          IconButton(
            onPressed: _submitComment,
            icon: Icon(Icons.send),
            style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
