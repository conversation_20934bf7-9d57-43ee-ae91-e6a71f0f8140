import 'package:flutter/material.dart';
import 'package:news_lexica_app/app/core/values/app_colors.dart';

class CommentBox extends StatefulWidget {
  final Function(String) onSubmit;
  final TextEditingController controller;
  const CommentBox({Key? key, required this.onSubmit, required this.controller})
    : super(key: key);

  @override
  _CommentBoxState createState() => _CommentBoxState();
}

class _CommentBoxState extends State<CommentBox> {
  final FocusNode _focusNode = FocusNode();

  @override
  void dispose() {
    widget.controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _submitComment() {
    if (widget.controller.text.trim().isNotEmpty) {
      widget.onSubmit(widget.controller.text.trim());
      widget.controller.clear();
      _focusNode.unfocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Expanded(
            child: Material(
              elevation: 1,
              borderRadius: BorderRadius.circular(24),
              child: TextField(
                controller: widget.controller,
                focusNode: _focusNode,
                textInputAction: TextInputAction.send,
                onSubmitted: (_) => _submitComment(),
                decoration: InputDecoration(
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 14,
                  ),
                  hintText: 'Write a comment...',
                  hintStyle: TextStyle(color: Colors.grey[500]),
                  filled: true,
                  fillColor: Colors.grey[100],
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(24),
                    borderSide: BorderSide.none,
                  ),
                ),
                style: TextStyle(fontSize: 16),
              ),
            ),
          ),
          SizedBox(width: 12),
          Material(
            color: theme.colorScheme.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(24),
            ),
            child: InkWell(
              borderRadius: BorderRadius.circular(24),
              onTap: _submitComment,
              child: Padding(
                padding: const EdgeInsets.all(14.0),
                child: Icon(Icons.send, color: Colors.white, size: 24),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
