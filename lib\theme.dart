import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:news_lexica_app/app/core/values/app_colors.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum FontSizes { small, medium, large }

class FontSizeController extends GetxController {
  var selectedFontSize = FontSizes.small.obs;

  void changeFontSize(FontSizes newSize) {
    selectedFontSize.value = newSize;
  }
}

Future<SharedPreferences> prefs = SharedPreferences.getInstance();
RxBool isLightTheme = false.obs;

saveThemeStatus() async {
  SharedPreferences pref = await prefs;
  pref.setBool('theme', isLightTheme.value);
}

getThemeStatus() async {
  var _isLight =
      prefs.then((SharedPreferences prefs) {
        return prefs.getBool('theme') ?? true;
      }).obs;
  isLightTheme.value = (await _isLight.value);
  Get.changeThemeMode(isLightTheme.value ? ThemeMode.light : ThemeMode.dark);
}

ThemeData darkTheme = ThemeData(
  useMaterial3: true,
  cardTheme: CardThemeData(color: Color(0xff1d5772), elevation: .1),
  navigationBarTheme: const NavigationBarThemeData(
    backgroundColor: Color(0xff2d3636),
    elevation: .09,
    height: 60,
  ),
  brightness: Brightness.dark,
  primaryColor: Colors.amber,
  buttonTheme: const ButtonThemeData(
    buttonColor: Colors.amber,
    disabledColor: Colors.grey,
  ),
);

ThemeData lightTheme1 = ThemeData(
  //  elevatedButtonTheme: ElevatedButtonThemeData(style: ElevatedButton.styleFrom(backgroundColor: Colors.teal,minimumSize:Size(Get.width, 50))),
  textTheme: GoogleFonts.signikaTextTheme().copyWith(
    bodySmall: TextStyle(fontSize: 12.0), // Example font size for body text
    bodyLarge: TextStyle(fontSize: 14.0), // Example font size for body text
    bodyMedium: TextStyle(
      fontSize: 16.0,
    ), // Example font size for another body text
    titleSmall: TextStyle(
      fontSize: 14.0,
    ), // Example font size for a headline text
    titleMedium: TextStyle(fontSize: 18.0),
    titleLarge: TextStyle(
      fontSize: 22.0,
    ), // Example font size for a headline text
  ),
  useMaterial3: true,
  dialogTheme: DialogThemeData(backgroundColor: Colors.white),
  dividerColor: Colors.grey,
  colorScheme: ColorScheme.fromSwatch(accentColor: Colors.teal),
  cardTheme: CardThemeData(color: Color(0xff319dc7), elevation: .1),
  navigationBarTheme: const NavigationBarThemeData(
    backgroundColor: Colors.white,
    elevation: .09,
    height: 60,
  ),
  scaffoldBackgroundColor: const Color(0xfffff8f2),
  appBarTheme: const AppBarTheme(
    elevation: .1,
    titleTextStyle: TextStyle(color: Colors.white, fontSize: 19),
    backgroundColor: AppColors.appBarColor,
    iconTheme: IconThemeData(color: Colors.white),
  ),
  brightness: Brightness.light,
  primaryColor: Colors.teal,

  buttonTheme: ButtonThemeData(
    buttonColor: Colors.blue, // Background color of the button
    textTheme: ButtonTextTheme.primary, // Text color
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8.0), // Rounded button shape
    ),
  ),
);

ThemeData lightTheme2 = ThemeData(
  textTheme: GoogleFonts.signikaTextTheme().copyWith(
    bodySmall: TextStyle(fontSize: 14.0), // Example font size for body text
    bodyMedium: TextStyle(fontSize: 16.0),
    bodyLarge: TextStyle(fontSize: 18.0), // Example font size for body text
    // Example font size for another body text
    titleMedium: TextStyle(fontSize: 14.0),
    titleSmall: TextStyle(fontSize: 20.0),
    titleLarge: TextStyle(
      fontSize: 24.0,
    ), // Example font size for a headline text
  ),
  useMaterial3: true,
  dialogTheme: DialogThemeData(backgroundColor: Colors.white),
  dividerColor: Colors.grey,
  colorScheme: ColorScheme.fromSwatch(accentColor: Colors.teal),
  cardTheme: CardThemeData(color: Color(0xff319dc7), elevation: .1),
  navigationBarTheme: const NavigationBarThemeData(
    backgroundColor: Colors.white,
    elevation: .09,
    height: 60,
  ),
  scaffoldBackgroundColor: const Color(0xfffff8f2),
  appBarTheme: const AppBarTheme(
    elevation: .1,
    titleTextStyle: TextStyle(color: Colors.white, fontSize: 19),
    backgroundColor: AppColors.appBarColor,
    iconTheme: IconThemeData(color: Colors.white),
  ),
  brightness: Brightness.light,
  primaryColor: Colors.teal,

  buttonTheme: ButtonThemeData(
    buttonColor: Colors.blue, // Background color of the button
    textTheme: ButtonTextTheme.primary, // Text color
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8.0), // Rounded button shape
    ),
  ),
);

ThemeData lightTheme3 = ThemeData(
  textTheme: GoogleFonts.signikaTextTheme().copyWith(
    bodySmall: TextStyle(fontSize: 20.0), // Example font size for body text
    bodyMedium: TextStyle(fontSize: 22.0),
    bodyLarge: TextStyle(fontSize: 24.0), // Example font size for body text
    // Example font size for another body text
    titleSmall: TextStyle(fontSize: 18.0),
    titleMedium: TextStyle(fontSize: 22.0),
    titleLarge: TextStyle(fontSize: 26.0),
    // Example font size for a headline text
  ),
  useMaterial3: true,
  dialogTheme: DialogThemeData(backgroundColor: Colors.white),
  dividerColor: Colors.grey,
  colorScheme: ColorScheme.fromSwatch(accentColor: Colors.teal),
  cardTheme: CardThemeData(color: Color(0xff319dc7), elevation: .1),
  navigationBarTheme: const NavigationBarThemeData(
    backgroundColor: Colors.white,
    elevation: .09,
    height: 60,
  ),
  scaffoldBackgroundColor: const Color(0xfffff8f2),
  appBarTheme: const AppBarTheme(
    elevation: .1,
    titleTextStyle: TextStyle(color: Colors.white, fontSize: 19),
    backgroundColor: AppColors.appBarColor,
    iconTheme: IconThemeData(color: Colors.white),
  ),
  brightness: Brightness.light,
  primaryColor: Colors.teal,

  buttonTheme: ButtonThemeData(
    buttonColor: Colors.blue, // Background color of the button
    textTheme: ButtonTextTheme.primary, // Text color
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8.0), // Rounded button shape
    ),
  ),
);

InputDecoration buildInputDecoration(String hintText) {
  return InputDecoration(
    prefixIconConstraints: const BoxConstraints(minHeight: 50, minWidth: 50),
    contentPadding: const EdgeInsets.only(left: 10.0, top: 12.0, bottom: 12.0),
    errorBorder: OutlineInputBorder(
      borderSide: const BorderSide(width: 1.0, color: Color(0xffe2e2e2)),
      borderRadius: BorderRadius.circular(5),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderSide: const BorderSide(width: 1.0, color: Color(0xffe2e2e2)),
      borderRadius: BorderRadius.circular(5),
    ),
    enabledBorder: OutlineInputBorder(
      borderSide: const BorderSide(width: 1.0, color: Color(0xffe2e2e2)),
      borderRadius: BorderRadius.circular(5),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(5),
      borderSide: const BorderSide(width: 1.0, color: Color(0xffe2e2e2)),
    ),
    hintText: hintText,
  );
}
