import 'package:get/get.dart';
import 'package:news_lexica_app/app/modules/app_heading_details/bindings/app_heading_details_bindings.dart';
import 'package:news_lexica_app/app/modules/app_heading_details/view/app_heading_details_view.dart';
import 'package:news_lexica_app/app/modules/authentication/otp_register/view/otp_register_view.dart';
import 'package:news_lexica_app/app/modules/category_list/bindings/category_list_bindings.dart';
import 'package:news_lexica_app/app/modules/category_list/view/category_list_view.dart';
import 'package:news_lexica_app/app/modules/exam_quiz_details/view/exam_quiz_details_view.dart';
import 'package:news_lexica_app/app/modules/exam_quiz_list/bindings/exam_quiz_list_bindings.dart';
import 'package:news_lexica_app/app/modules/free_lesson/view/free_lesson.dart';
import 'package:news_lexica_app/app/modules/gre_quiz_details/bindings/gre_quiz_details_bindings.dart';
import 'package:news_lexica_app/app/modules/gre_quiz_details/view/gre_quiz_details_view.dart';
import 'package:news_lexica_app/app/modules/gre_quiz_list/bindings/gre_quiz_list_bindings.dart';
import 'package:news_lexica_app/app/modules/gre_quiz_list/view/gre_quiz_list_view.dart';
import 'package:news_lexica_app/app/modules/intro_video_play/bindings/video_play_bindings.dart';
import 'package:news_lexica_app/app/modules/lesson_category_page/bindings/lesson_cetegory_bindings.dart';
import 'package:news_lexica_app/app/modules/lesson_details/bindigns/lesson_details_bindings.dart';
import 'package:news_lexica_app/app/modules/lesson_discussion/bindigns/quiz_discussion_bindings.dart';
import 'package:news_lexica_app/app/modules/lesson_list_two/bindings/lesson_list_two_bindings.dart';
import 'package:news_lexica_app/app/modules/lesson_list_two/view/lesson_list_two.dart';
import 'package:news_lexica_app/app/modules/lesson_reading/bindigns/lesson_reading_bindings.dart';
import 'package:news_lexica_app/app/modules/lesson_reading/view/lesson_readin_view.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/book_list/bindings/book_list_bindings.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/book_list/view/book_list_view.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/quiz1/bindings/ielts_quiz_1_bindings.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/quiz2/bindings/ielts_quiz_2_bindings.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/quiz2/view/ielts_quiz_2_view.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/quiz_practice_example/bindings/quiz_practice_exmple_bindings.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/quiz_practice_example/view/quiz_practice_example_view.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/reading_pasage_details/bindings/reading_passage_details_bindings.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/reading_passage/bindings/reading_passage_bindings.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/reading_passage/view/reading_passage_view.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/story_reading/bindings/story_reading_bindings.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/story_reading/view/story_reading_view.dart';
import 'package:news_lexica_app/app/modules/news/news_list/bindings/news_list_bindings.dart';
import 'package:news_lexica_app/app/modules/news/news_reding/bindings/new_reading_bindings.dart';
import 'package:news_lexica_app/app/modules/news/news_reding/view/new_reading_view.dart';
import 'package:news_lexica_app/app/modules/quiz_mistakes/bindigns/quiz_mistake_bindings.dart';
import 'package:news_lexica_app/app/modules/quiz_mistakes/view/quiz_mistake_view.dart';
import 'package:news_lexica_app/app/modules/quiz_one/bindigns/quiz_one_bindings.dart';
import 'package:news_lexica_app/app/modules/quiz_one/view/quiz_one_view.dart';
import 'package:news_lexica_app/app/modules/server_off/bidings/server_off_bindings.dart';
import 'package:news_lexica_app/app/modules/server_off/view/server_off_view.dart';
import 'package:news_lexica_app/app/modules/splash/bindings/splash_bindings.dart';
import 'package:news_lexica_app/app/modules/splash/view/splash_view.dart';
import 'package:news_lexica_app/app/modules/subscription/bindings/subscribe_bindings.dart';
import 'package:news_lexica_app/app/modules/translation/bindings/translation_binings.dart';
import 'package:news_lexica_app/app/modules/translation/view/translation_view.dart';
import 'package:news_lexica_app/app/modules/user_profile_details/bindings/user_profile_details_bindings.dart';
import 'package:news_lexica_app/app/modules/user_profile_details/view/user_profile_details_view.dart';

import '../modules/authentication/forget_change_pass/bindigns/forget_change_pass_bindings.dart';
import '../modules/authentication/forget_change_pass/view/forget_change_pass_view.dart';
import '../modules/authentication/forget_pass/bindigns/forget_pass_bindings.dart';
import '../modules/authentication/forget_pass/view/forget_pass_view.dart';
import '../modules/authentication/login_user/bindigns/login_user_bindings.dart';
import '../modules/authentication/login_user/view/login_user_view.dart';
import '../modules/authentication/otp_register/bindigns/forget_pass_bindings.dart';

import '../modules/authentication/register_user/bindigns/register_user_bindings.dart';
import '../modules/authentication/register_user/view/register_user_view.dart';
import '../modules/exam_quiz_details/bindings/exam_quiz_details_bindings.dart';
import '../modules/exam_quiz_list/view/exam_quiz_list_view.dart';
import '../modules/free_lesson/bindings/free_lessoon_bindings.dart';
import '../modules/intro_video_play/view/video_play_view.dart';
import '../modules/lesson_category_page/view/lesson_category_view.dart';
import '../modules/lesson_details/view/lesson_details_view.dart';
import '../modules/lesson_discussion/view/quiz_discussion_view.dart';
import '../modules/new_ielts_module/quiz1/view/ielts_quiz_1_view.dart';
import '../modules/new_ielts_module/reading_list/bindings/reading_list_bindings.dart';
import '../modules/new_ielts_module/reading_list/view/reading_list_view.dart';
import '../modules/new_ielts_module/reading_pasage_details/view/reading_passage_details_view.dart';
import '../modules/news/news_list/view/new_list_view.dart';
import '../modules/quiz_three/bindigns/quiz_three_bindings.dart';
import '../modules/quiz_three/view/quiz_three_view.dart';
import '../modules/quiz_two/bindigns/quiz_two_bindings.dart';
import '../modules/quiz_two/view/quiz_two_view.dart';
import '../modules/season_compete_info/bindings/season_compete_info_bindings.dart';
import '../modules/season_compete_info/view/season_compete_info_view.dart';
import '../modules/subscription/view/subscribe_view.dart';
import '/app/modules/favorite/bindings/favorite_binding.dart';
import '/app/modules/favorite/views/favorite_view.dart';
import '/app/modules/home/<USER>/home_binding.dart';
import '/app/modules/home/<USER>/home_view.dart';
import '/app/modules/main/bindings/main_binding.dart';
import '/app/modules/main/views/main_view.dart';
import '/app/modules/other/bindings/other_binding.dart';
import '/app/modules/other/views/other_view.dart';

import '/app/modules/settings/bindings/settings_binding.dart';
import '/app/modules/settings/views/settings_view.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = Routes.SPLASH_WELCOME;

  static final routes = [
    GetPage(
      name: _Paths.MAIN,
      page: () => MainView(),
      binding: MainBinding(),
    ),
    GetPage(
      name: _Paths.HOME,
      page: () => HomeView(),
      binding: HomeBinding(),
    ),
    GetPage(
      name: _Paths.FAVORITE,
      page: () => FavoriteView(),
      binding: FavoriteBinding(),
    ),
    GetPage(
      name: _Paths.SETTINGS,
      page: () => SettingsView(),
      binding: SettingsBinding(),
    ),
    GetPage(
      name: _Paths.OTHER,
      page: () => OtherView(),
      binding: OtherBinding(),
    ),
    GetPage(
      name: _Paths.USER_LOGIN,
      page: () => LoginUserView(),
      binding: LoginUserBindings(),
    ),
    GetPage(
      name: _Paths.USER_REGISTER,
      page: () => RegisterUserView(),
      binding: RegisterUserBindings(),
    ),
    GetPage(
      name: _Paths.USER_OTP_RGISTER,
      page: () => OtpRegisterView(),
      binding: OtpRegisterBindings(),
    ),
    GetPage(
      name: _Paths.LESSON_DETAILS,
      page: () => LessonDetailsView(),
      binding: LessonDetailsBindings(),
    ),
    GetPage(
      name: _Paths.LESSON_READING,
      page: () => LessonReadingView(),
      binding: LessonReadingBindings(),
    ),
    GetPage(
      name: _Paths.LESSON_QUIZ_ONE,
      page: () => QuizOneView(),
      binding: QuizOneBindings(),
    ),
    GetPage(
      name: _Paths.LESSON_QUIZ_TWO,
      page: () => QuizTwoView(),
      binding: QuizTwoBindings(),
    ),
    GetPage(
      name: _Paths.LESSON_QUIZ_THREE,
      page: () => QuizThreeView(),
      binding: QuizThreeBindings(),
    ),
    GetPage(
      name: _Paths.LESSON_CATEGORY,
      page: () => LessonCategoryView(),
      binding: LessonCategoryBindings(),
    ),
    GetPage(
      name: _Paths.ALL_CATEGORY,
      page: () => CategoryListView(),
      binding: CategoryListBindings(),
    ),
    GetPage(
      name: _Paths.LESSON_QUIZ_MISTAKE,
      page: () => QuizMistakeView(),
      binding: QuizMistakeBindings(),
    ),
    GetPage(
      name: _Paths.LESSON_QUIZ_DISCUSSION,
      page: () => QuizDiscussionView(),
      binding: QuizDiscussionBindings(),
    ),
    GetPage(
      name: _Paths.USER_SUBSCRBE,
      page: () => SubscribeView(),
      binding: SubscribeBindings(),
    ),
    GetPage(
      name: _Paths.USER_PROFILE,
      page: () => UserProfileDetailView(),
      binding: UserProfileDetailsBindings(),
    ),
    GetPage(
      name: _Paths.LESSON_LIST_TWO,
      page: () => LessonListTwoView(),
      binding: LessonListTwoBindings(),
    ),
    GetPage(
      name: _Paths.UPDDATE_PASSWORD,
      page: () => ForgetChangePassView(),
      binding: ForgetPassChangeBindings(),
    ),
    GetPage(
      name: _Paths.USER_FORGETPASS,
      page: () => ForgetPassView(),
      binding: ForgetPassBindings(),
    ),
    GetPage(
      name: _Paths.INTRO_VIDEO,
      page: () => VideoPLayView(),
      binding: VideoPLayBindings(),
    ),
    GetPage(
      name: _Paths.EXAM_QUIZ_LIST,
      page: () => ExamQuizListView(),
      binding: ExamQuizListBindings(),
    ),
    GetPage(
      name: _Paths.EXAM_QUIZ_DETAILS,
      page: () => ExamQuizDetailsView(),
      binding: ExamQuizDetailsBindings(),
    ),
    GetPage(
      name: _Paths.SPLASH_WELCOME,
      page: () => SplashView(),
      binding: SplashBindings(),
    ),
    GetPage(
      name: _Paths.COMPETATION_INFO,
      page: () => SeasonCompeteInfoView(),
      binding: SeasonCompeteInfoBindings(),
    ),
    GetPage(
      name: _Paths.SERVER_OFF,
      page: () => ServerOffView(),
      binding: ServerOffBindings(),
    ),
    GetPage(
      name: _Paths.GRE_QUI_DETAILS,
      page: () => GreQuizDetailsView(),
      binding: GreQuizDetailsBindings(),
    ),
    GetPage(
      name: _Paths.GRE_QUIZ_LIST,
      page: () => GreQuizListView(),
      binding: GreQuizListBindings(),
    ),
    GetPage(
      name: _Paths.TRANSLATION_WORDS,
      page: () => TranslationView(),
      binding: TranslationBinings(),
    ),
    GetPage(
      name: _Paths.NEWS_PAPER_LIST,
      page: () => NewListView(),
      binding: NewsListBindings(),
    ),
    GetPage(
      name: _Paths.NEWS_PAPER_READING,
      page: () => NewReadingView(),
      binding: NewReadingBindings(),
    ),
    GetPage(
      name: _Paths.APP_HEADING_DETAILS,
      page: () => AppHeadingDetailsView(),
      binding: AppHeadingDetailsBindings(),
    ),
    GetPage(
      name: _Paths.NEW_IELTS_BOOK_LIST,
      page: () => BookListView(),
      binding: BookListBindings(),
    ),
    GetPage(
      name: _Paths.NEW_IELTS_BOOK_LIST,
      page: () => BookListView(),
      binding: BookListBindings(),
    ),
    GetPage(
      name: _Paths.NEW_IELTS_BOOK_LIST,
      page: () => BookListView(),
      binding: BookListBindings(),
    ),
    GetPage(
      name: _Paths.NEW_IELTS_READING_PASSAGE_LIST,
      page: () => ReadingPassageView(),
      binding: ReadingPassageBindings(),
    ),
    GetPage(
      name: _Paths.NEW_IELTS_READING_LIST,
      page: () => ReadingListView(),
      binding: ReadingListBindings(),
    ),
    GetPage(
      name: _Paths.NEW_IELTS_READING_PASSAGE_DETAILS,
      page: () => ReadingPassageDetailsView(),
      binding: ReadingPassageDetailsBindings(),
    ),
    GetPage(
      name: _Paths.IELTS_STORY_READING_PAGE,
      page: () => StoryReadingView(),
      binding: StoryReadingBindings(),
    ),
    GetPage(
      name: _Paths.IELTS_NEW_QUIZ_ONE,
      page: () => IeltsQuiz1View(),
      binding: IeltsQuiz1Bindings(),
    ),
    GetPage(
      name: _Paths.IELTS_NEW_QUIZ_TWO,
      page: () => IeltsQuiz2View(),
      binding: IeltsQuiz2Bindings(),
    ),
    GetPage(
      name: _Paths.IELTS_NEW_QUIZ_EXMPLE,
      page: () => QuizPracticeExampleView(),
      binding: QuizPracticeExmpleBindings(),
    ),
    GetPage(
      name: _Paths.FREE_LESSON_VIEW,
      page: () => FreeLessonView(),
      binding: FreeLessonBindings(),
    ),
  ];
}
