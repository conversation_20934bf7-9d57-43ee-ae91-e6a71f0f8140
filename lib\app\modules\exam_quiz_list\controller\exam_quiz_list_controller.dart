import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import 'package:news_lexica_app/app/modules/main/controllers/main_controller.dart';

class ExamQuizListController extends BaseController {
  var isSubscribed = false.obs;

  MainController mainController = Get.find();

  @override
  void onInit() {
    isSubscribed = mainController.isSubscribed;
    super.onInit();
  }
}
