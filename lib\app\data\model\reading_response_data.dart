class ReadingResponseData {
  List<ReadingList>? readingList;

  ReadingResponseData({this.readingList});

  ReadingResponseData.fromJson(Map<String, dynamic> json) {
    if (json['readingList'] != null) {
      readingList = <ReadingList>[];
      json['readingList'].forEach((v) {
        readingList!.add(new ReadingList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.readingList != null) {
      data['readingList'] = this.readingList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ReadingList {
  int? id;
  String? name;
  int? ieltsBookId;
  String? createdAt;
  String? updatedAt;

  ReadingList(
      {this.id, this.name, this.ieltsBookId, this.createdAt, this.updatedAt});

  ReadingList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    ieltsBookId = json['ieltsBookId'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['ieltsBookId'] = this.ieltsBookId;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    return data;
  }
}
