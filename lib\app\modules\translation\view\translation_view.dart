import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:news_lexica_app/app/core/widget/custom_app_bar.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../../../core/base/base_view.dart';
import '../controller/translation_controller.dart';

class TranslationView extends BaseView<TranslationController>{
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return CustomAppBar(appBarTitleText: 'Translate');
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return WebViewWidget(controller: controller.webController);
    throw UnimplementedError();
  }

}