import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/values/app_url.dart';
import 'package:news_lexica_app/app/core/widget/custom_app_bar.dart';

import '../controller/user_profile_details_controller.dart';

class UserProfileDetailView extends BaseView<UserProfileDetailsController> {
  UserProfileDetailView() {
    controller.getUserData();
  }
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return CustomAppBar(appBarTitleText: "My Profile");
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          Obx(() => Column(
                children: [
                  const SizedBox(
                    height: 20,
                  ),
                  CircleAvatar(
                    backgroundColor: Colors.blueAccent,
                    radius: 50,
                    child: CachedNetworkImage(
                      imageUrl:
                          "${AppUrl.imageUrl}${controller.userUiData.photo}",
                      progressIndicatorBuilder:
                          (context, url, downloadProgress) =>
                              CircularProgressIndicator(
                                  value: downloadProgress.progress),
                      errorWidget: (context, url, error) => InkWell(
                        child: CircleAvatar(
                          radius: 40,
                          child: Image.asset("assets/upload_image.png"),
                        ),
                      ),
                    ),
                  )
                ],
              )),
          Card(
              child: ListTile(
            title: Text("User Name"),
            subtitle: Obx(() => Text("${controller.userUiData.name}")),
          )),
          Card(
              child: ListTile(
            title: Text("Phone Number"),
            subtitle: Obx(() => Text("${controller.userUiData.phone}")),
          )),
          Card(
              child: ListTile(
            title: Text("Your Score"),
            subtitle: Obx(() => Text("${controller.userUiData.score}")),
          )),
        ],
      ),
    );

    throw UnimplementedError();
  }
}
