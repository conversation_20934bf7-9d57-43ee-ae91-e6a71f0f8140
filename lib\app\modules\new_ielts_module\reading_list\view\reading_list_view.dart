import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/widget/custom_app_bar.dart';
import '../controller/reading_list_controller.dart';
import '../widget/book_list_card.dart';

class ReadingListView extends BaseView<ReadingListController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return CustomAppBar(
      appBarTitleText: "Reading List",
    );
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return Obx(() => Padding(
          padding: const EdgeInsets.all(8.0),
          child: ListView.builder(
              itemCount: controller.bookList.length,
              itemBuilder: (context, index) {
                var reading = controller.bookList[index];
                return AnimationConfiguration.staggeredList(
                    position: index,
                    duration: const Duration(milliseconds: 800),
                    child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                            child: ReadingListCard(book: reading))));
              }),
        ));
    throw UnimplementedError();
  }
}
