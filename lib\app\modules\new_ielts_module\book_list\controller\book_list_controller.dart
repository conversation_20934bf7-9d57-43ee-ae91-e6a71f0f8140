import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import 'package:news_lexica_app/app/data/model/book_response_data.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/book_list/model/book_data_model.dart';

import '../../../../data/repository/lesson_repository.dart';

class BookListController extends BaseController {
  final RxList<BookDataModel> _bookListController = RxList.empty();

  List<BookDataModel> get bookList => _bookListController.toList();

  final LessonRepository _repository = Get.find(
    tag: (LessonRepository).toString(),
  );

  getBookList() {
    var bookListService = _repository.getBookList();
    callDataService(
      bookListService,
      onSuccess: _handleBookListResponseSuccess,
      onError: _handleBookListError,
    );
  }

  _handleBookListResponseSuccess(BookResponseData response) {
    _bookListController.clear();
    _bookListController.addAll(
      response.booksList!.map((e) => BookDataModel.fromData(e.toJson())),
    );
  }

  _handleBookListError(Exception exception) {}

  @override
  void onInit() {
    getBookList();
    super.onInit();
  }
}
