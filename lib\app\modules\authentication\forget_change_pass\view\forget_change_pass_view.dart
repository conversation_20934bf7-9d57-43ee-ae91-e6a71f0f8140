import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/values/app_values.dart';
import '../../../../core/values/app_colors.dart';
import '../controller/forget_change_pass_controller.dart';

class ForgetChangePassView extends BaseView<ForgetChangePassController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: Form(
        //  key: formKey,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Sized<PERSON><PERSON>(height: 25),
              Image.asset('assets/forgot-password.png', height: 100),

              <PERSON><PERSON><PERSON><PERSON>(height: 25),
              <PERSON><PERSON><PERSON><PERSON>(height: 25),
              _text<PERSON><PERSON><PERSON><PERSON>(
                controller.passwordController,
                'Update Password',
                'Enter 4 digit password',
              ),

              ElevatedButton(
                onPressed: () {
                  controller.changePassword();
                },
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size.fromHeight(40),
                  backgroundColor: Colors.teal,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5),
                  ),
                ),
                child: Text(
                  "Change Password",
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
    throw UnimplementedError();
  }

  Widget _textPassField(
    TextEditingController textController,
    String title,
    String hintText,
  ) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),

          SizedBox(height: AppValues.padding),
          Obx(
            () => TextFormField(
              controller: textController,
              decoration: InputDecoration(
                suffixIcon: IconButton(
                  icon: Icon(
                    controller.isPasswordObscured.value
                        ? Icons.visibility
                        : Icons.visibility_off,
                  ),
                  onPressed: () {
                    controller.togglePasswordVisibility();
                  },
                ),
                border: OutlineInputBorder(
                  borderSide: BorderSide.none,
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: AppColors.textFieldColor,
                hintText: hintText,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
