import 'dart:math';

import 'package:audioplayers/audioplayers.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import 'package:news_lexica_app/app/data/model/exam_quiz_list.dart';

import '../../../core/model/quiz.dart';
import '../../../core/model/update_user_score_body_prem.dart';
import '../../../data/repository/lesson_repository.dart';
import '../../../data/repository/user_repository.dart';
import '../../lesson_details/model/quiz_three_ui_data.dart';
import '../../quiz_one/controller/quiz_one_controller.dart';

class ExamQuizDetailsController extends BaseController {
  final UserRepository _repositoryUser = Get.find(
    tag: (UserRepository).toString(),
  );

  final LessonRepository _repository = Get.find(
    tag: (LessonRepository).toString(),
  );
  var startIndex = 0.obs;
  var endIndex = 3.obs;
  var list = <Answer>[].obs;

  var currentIndex = 0.obs;
  var quiz3 = <Quiz>[].obs;

  final player = AudioPlayer();
  getNextQuestion() {
    startIndex.value = endIndex.value;
    endIndex.value = (endIndex.value + 3).clamp(0, quiz3.length);
  }

  Random random = Random();

  clearQuiz() {
    startIndex.value = 0;
    endIndex.value = 5;
    currentIndex.value = 0;
  }

  previousQuestion() {
    endIndex.value = startIndex.value;
    startIndex.value = (startIndex.value - 3).clamp(0, quiz3.length);
  }

  bool isChoiceSelected(int questionIndex, int choiceIndex) {
    return quiz3[questionIndex].selectedChoice == choiceIndex;
  }

  void toggleChoice(int questionIndex, int choiceIndex) {
    final question = quiz3[questionIndex];
    question.selectedChoice = choiceIndex;
  }

  resetQuiz() {
    currentIndex.value = 0;
  }

  nextQuestion() => currentIndex++;

  void initializeQuizzes() {
    quiz3.shuffle();
  }

  bool quizAvailable() {
    if (currentIndex.value + 1 >= quiz3.length) {
      return false;
    } else {
      return true;
    }
  }

  getExamQuizList(int lessonNumber) {
    var quizListService = _repository.getExamQuizListByLesson(lessonNumber);
    callDataService(quizListService, onSuccess: _handleExamQuizGetSuccess);
  }

  bool quizCheckAnswer(String answer, Quiz quiz) {
    return quiz.answer.toString().toLowerCase().trim() ==
            answer.toLowerCase().trim()
        ? true
        : false;
  }

  getLesson(List<QuizThreeUiData> lesson) {
    lesson.forEach((element) {
      quiz3.add(
        Quiz(
          choices: [
            element.option1,
            element.option2,
            element.option3,
            element.option4,
          ],
          question: element.question,
          answer: element.answer,
        ),
      );
    });
  }

  @override
  void onInit() {
    var dataModel = Get.arguments;
    if (dataModel is int) {
      getExamQuizList(dataModel);
    }
    super.onInit();
  }

  updateScore(String score) {
    // var userId = GetStorage().read('id');

    var updateScoreService = _repositoryUser.updateUserScore(
      UpdateUserScoreBodyPrem(userId: "", score: score),
    );
    callDataService(
      updateScoreService,
      onSuccess: _handleScoreUpdateSuccess,
      onError: _handleScoreUpdateError,
    );
  }

  _handleExamQuizGetSuccess(ExamQuizResponseData response) {
    List<QuizThreeUiData>? quizThreeList =
        response.message
            ?.map(
              (e) => QuizThreeUiData(
                id: e.id != null ? e.id! : 0,
                answer: e.answer != null ? e.answer! : "Null",
                option1: e.option1 != null ? e.option1! : "Null",
                option2: e.option2 != null ? e.option2! : "Null",
                option3: e.option3 != null ? e.option3! : "Null",
                option4: e.option4 != null ? e.option4! : "Null",
                question: e.question != null ? e.question! : "Null",
              ),
            )
            .toList();

    getLesson(quizThreeList!);
    list.value = List.generate(quizThreeList.length, (index) => Answer.pending);
  }

  _handleScoreUpdateSuccess(Map<String, dynamic> response) {
    Get.back();
    Get.back();
  }

  _handleScoreUpdateError(Exception exception) {
    Fluttertoast.showToast(msg: errorMessage);
    Get.back();
    Get.back();
  }

  String getQuizImage(int score, int quizSize) {
    var percentage = (score / quizSize) * 100;
    if (percentage > 80) {
      return "assets/gold.png";
    } else if (percentage > 60) {
      return "assets/silver.png";
    }
    if (percentage > 30) {
      return "assets/bronze.png";
    } else {
      return "assets/failed.png";
    }
  }

  String getQuizText(int score, int quizSize) {
    var percentage = (score / quizSize) * 100;
    if (percentage > 80) {
      return "Brilliant Work!";
    } else if (percentage > 60) {
      return "Excellent Work!";
    }
    if (percentage > 30) {
      return "Good Work";
    } else {
      return "Try again latter";
    }
  }
}
