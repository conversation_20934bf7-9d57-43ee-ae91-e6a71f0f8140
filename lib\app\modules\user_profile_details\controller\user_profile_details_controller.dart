import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import '../../../data/model/user_data.dart';
import '../../../data/repository/user_repository.dart';
import '../../settings/model/user_ui_data.dart';

class UserProfileDetailsController extends BaseController {
  final UserRepository _repositoryUser =
      Get.find(tag: (UserRepository).toString());

  var score = 0.obs;
  final Rx<UserUiData> _userUiDataController = Rx(UserUiData());
  UserUiData get userUiData => _userUiDataController.value;

  void getUserData() {
    var future = _repositoryUser.getUserData("");
    callDataService(future, onSuccess: _handleSuccess, onError: _handleError);
  }

  _handleSuccess(UserData response) {
    _userUiDataController(UserUiData(
      id: response.message?.id != null ? response.message?.id! : 0,
      name: response.message?.name ?? "",
      photo: response.message?.photo ?? "",
      phone: response.message?.phone ?? '0',
      subscription: response.message?.subscriptionRequest == null
          ? false
          : response.message?.subscriptionRequest!.subscribed == false
              ? false
              : true,
      score: response.message?.scoreBoard?.score.toString() ?? "",
    ));
  }

  _handleError(Exception e) {}

  _handleUserScoreSuccess(Map<String, dynamic> response) {
    score(response['score']);
  }
}
