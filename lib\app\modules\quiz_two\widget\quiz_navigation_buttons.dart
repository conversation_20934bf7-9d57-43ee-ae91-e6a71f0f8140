import 'package:flutter/material.dart';
import '../controller/quiz_two_controller.dart';

class QuizNavigationButtons extends StatelessWidget {
  final QuizTwoController controller;
  final VoidCallback onPrevious;
  final VoidCallback onNext;

  const QuizNavigationButtons({
    Key? key,
    required this.controller,
    required this.onPrevious,
    required this.onNext,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildNavigationButton(
              context: context,
              label: "Previous Page",
              icon: Icons.arrow_back_rounded,
              onPressed: onPrevious,
              isPrimary: false,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildNavigationButton(
              context: context,
              label: "Next Page",
              icon: Icons.arrow_forward_rounded,
              onPressed: onNext,
              isPrimary: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButton({
    required BuildContext context,
    required String label,
    required IconData icon,
    required VoidCallback onPressed,
    required bool isPrimary,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(
        icon,
        size: 18,
        color: isPrimary ? Colors.white : Colors.teal.shade600,
      ),
      label: Text(
        label,
        style: TextStyle(
          color: isPrimary ? Colors.white : Colors.teal.shade600,
          fontWeight: FontWeight.w600,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: isPrimary ? Colors.teal.shade600 : Colors.transparent,
        foregroundColor: isPrimary ? Colors.white : Colors.teal.shade600,
        elevation: isPrimary ? 2 : 0,
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side:
              isPrimary
                  ? BorderSide.none
                  : BorderSide(color: Colors.teal.shade300, width: 2),
        ),
      ),
    );
  }
}
