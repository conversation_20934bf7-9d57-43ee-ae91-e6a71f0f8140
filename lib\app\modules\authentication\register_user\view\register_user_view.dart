import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/values/app_colors.dart';
import 'package:news_lexica_app/app/routes/app_pages.dart';

import '../../../../core/values/app_values.dart';
import '../controller/register_user_controller.dart';

class RegisterUserView extends BaseView<RegisterUserController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: AppValues.halfPadding),
        child: Form(
          key: formKey,
          child: Column(
            children: [
              SizedBox(height: AppValues.extraLargeSpacing),
              _topBannerCard(context),
              _textField(
                controller.nameController,
                "Name",
                "Enter your name",
                "Name",
              ),
              _textField(
                controller.phoneController,
                "Phone",
                "01XXXXXXXXX",
                "Num",
              ),
              _textPassField(
                controller.passwordController,
                "Password",
                "Enter 4 digit password",
              ),
              SizedBox(height: AppValues.largePadding),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    minimumSize: Size(Get.height * .9, 50),
                    backgroundColor: Colors.teal,
                  ),
                  onPressed: () {
                    if (formKey.currentState!.validate()) {
                      controller.registerUpUser();
                    }
                  },
                  child: Text(
                    "Register",
                    style: Theme.of(
                      context,
                    ).textTheme.titleLarge!.copyWith(color: Colors.white),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 10, bottom: 10),
                child: Text("Already Have an account?"),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: ElevatedButton(
                  onPressed: () => Get.toNamed(Routes.USER_LOGIN),
                  child: Text(
                    "Login",
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  style: ElevatedButton.styleFrom(
                    elevation: 0.0,
                    minimumSize: Size(Get.height * .9, 50),
                    backgroundColor: Colors.transparent,
                    side: BorderSide(color: Colors.teal),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
    throw UnimplementedError();
  }

  Widget _textPassField(
    TextEditingController textController,
    String title,
    String hintText,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        Obx(
          () => TextFormField(
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter some text';
              }
              if (value.length != 4) {
                return 'Password must be exactly 4 characters long';
              }
              return null;
            },
            controller: textController,
            inputFormatters: [
              LengthLimitingTextInputFormatter(4), // Limit to 4 characters
            ],
            decoration: InputDecoration(
              suffixIcon: IconButton(
                icon: Icon(
                  controller.isPasswordObscured.value
                      ? Icons.visibility
                      : Icons.visibility_off,
                ),
                onPressed: () {
                  controller.togglePasswordVisibility();
                },
              ),
              border: OutlineInputBorder(
                borderSide: BorderSide.none,
                borderRadius: BorderRadius.circular(8),
              ),
              filled: true,
              hintText: hintText,
            ),
            obscureText:
                !controller
                    .isPasswordObscured
                    .value, // Hide the input by default
          ),
        ),
      ],
    );
  }

  Widget _textField(
    TextEditingController controller,
    String title,
    String hintText,
    String type,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        TextFormField(
          validator: customValidator(type),
          controller: controller,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.circular(8),
            ),
            filled: true,
            hintText: hintText,
          ),
        ),
      ],
    );
  }

  String? Function(String?) customValidator(String type) {
    return (value) {
      if (value == null || value.isEmpty) {
        return 'Please enter some text';
      }

      if (type == "Num" && value.length != 11) {
        return 'Number must be exactly 11 digits';
      }

      if (type == "Name" && value.length > 20) {
        return 'Name must be under 20 characters';
      }

      return null;
    };
  }

  Widget _topBannerCard(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset('assets/lexica_top_icon.png', height: 25),
            SizedBox(width: 5),
            Text(
              'NEWS',
              style: GoogleFonts.poppins(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Color(0xff2734A1),
              ),
            ),
            SizedBox(width: 2),
            Text(
              'LEXICA',
              style: GoogleFonts.poppins(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ],
        ),
        Text(
          "Your Vocabulary Trainer",
          style: Theme.of(context).textTheme.titleSmall!.copyWith(fontSize: 18),
        ),
      ],
    );
  }
}
