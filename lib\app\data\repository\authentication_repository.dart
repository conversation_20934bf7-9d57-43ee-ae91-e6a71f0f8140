import '../../core/model/signup_body_prem.dart';
import '../../core/model/update_user_pass_body_prem.dart';

abstract class AuthenticationRepository{
  Future<Map<String,dynamic>>signInUser(String phone, String password);
  Future<Map<String,dynamic>>checkUserExist(String phone);
  Future<Map<String,dynamic>>registerUser(SignUpBodyPrem signUpBodyPrem);
  Future<Map<String,dynamic>>sendOtp(String phone);
  Future<Map<String,dynamic>>matchOtp(String enteredOtp,String secretOtp);
  Future<Map<String,dynamic>> updateUserPassword(UserPassBodyPrem userPassBodyPrem);
  Future<Map<String,dynamic>>checkPasswordMatch(String oldPassword);
  Future<Map<String,dynamic>>checkServer();
}