import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:in_app_update/in_app_update.dart';
import '../l10n/app_localizations.dart';
import '../theme.dart';
import '/app/bindings/initial_binding.dart';
import '/app/routes/app_pages.dart';
import '/flavors/build_config.dart';
import '/flavors/env_config.dart';
import 'package:android_intent_plus/android_intent.dart';

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  Future<void> checkForUpdate() async {
    InAppUpdate.checkForUpdate()
        .then((info) {
          if (info.updateAvailability == UpdateAvailability.updateAvailable) {
            setState(() {
              update();
            });
          }
        })
        .catchError((e) {});
  }

  void update() async {
    await InAppUpdate.performImmediateUpdate().catchError(
      (e) => Fluttertoast.showToast(msg: e),
    );
  }

  FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  Future<void> requestExactAlarmPermission() async {
    if (await canScheduleExactAlarms()) {
      return;
    }
    const AndroidIntent intent = AndroidIntent(
      action: 'android.settings.REQUEST_SCHEDULE_EXACT_ALARM',
    );
    await intent.launch();
  }

  Future<bool> canScheduleExactAlarms() async {
    return await FlutterLocalNotificationsPlugin()
            .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin
            >()
            ?.canScheduleExactNotifications() ??
        false;
  }

  void notificationHandler() {}

  final EnvConfig _envConfig = BuildConfig.instance.config;
  FontSizeController fontSizeController1 = Get.put(FontSizeController());
  void initState() {
    getThemeStatus();
    checkForUpdate();
    // requestExactAlarmPermission();
    // canScheduleExactAlarms();
    //  notificationHandler();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => GetMaterialApp(
        title: _envConfig.appName,
        initialRoute: AppPages.INITIAL,
        initialBinding: InitialBinding(),
        getPages: AppPages.routes,
        localizationsDelegates: AppLocalizations.localizationsDelegates,
        supportedLocales: _getSupportedLocal(),
        themeMode: ThemeMode.system,
        theme:
            fontSizeController1.selectedFontSize.value == FontSizes.small
                ? lightTheme1
                : fontSizeController1.selectedFontSize.value == FontSizes.medium
                ? lightTheme2
                : lightTheme3,
        darkTheme: darkTheme,
        debugShowCheckedModeBanner: false,
      ),
    );
  }

  List<Locale> _getSupportedLocal() {
    return [const Locale('en', ''), const Locale('bn', '')];
  }
}
