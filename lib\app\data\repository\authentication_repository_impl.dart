import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/model/signup_body_prem.dart';
import 'package:news_lexica_app/app/core/model/update_user_pass_body_prem.dart';
import 'package:news_lexica_app/app/data/remote/authentication_remote_data_source.dart';
import 'package:news_lexica_app/app/data/repository/authentication_repository.dart';

class AuthenticationRepositoryImpl implements AuthenticationRepository{
  final AuthenticationRemoteDataSource _remoteSource =
  Get.find(tag: (AuthenticationRemoteDataSource).toString());




  @override
  Future<Map<String, dynamic>> checkPasswordMatch(String oldPassword) {
    return _remoteSource.checkPasswordMatch(oldPassword);
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> checkUserExist(String phone) {
    return _remoteSource.checkUserExist(phone);
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> matchOtp(String enteredOtp, String secretOtp) {
    return _remoteSource.matchOtp(enteredOtp, secretOtp);
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> registerUser(SignUpBodyPrem signUpBodyPrem) {
    return _remoteSource.registerUser(signUpBodyPrem);
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> sendOtp(String phone) {
    return _remoteSource.sendOtp(phone);
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> signInUser(String phone, String password) {
    return _remoteSource.signInUser(phone, password);
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> updateUserPassword(UserPassBodyPrem userPassBodyPrem) {
    return _remoteSource.updateUserPassword(userPassBodyPrem);
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> checkServer() {
    return _remoteSource.checkServer();
    throw UnimplementedError();
  }



}