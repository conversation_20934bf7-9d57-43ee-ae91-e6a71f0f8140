import 'package:flutter/material.dart';
import '../controller/quiz_two_controller.dart';

class QuizQuestionCard extends StatelessWidget {
  final dynamic question; // Replace with your actual question type
  final TextEditingController textController;
  final Answer answerState;
  final VoidCallback onSubmit;

  const QuizQuestionCard({
    Key? key,
    required this.question,
    required this.textController,
    required this.answerState,
    required this.onSubmit,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: _getBorderColor(), width: 2),
      ),
      color: _getCardColor(context),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              question.question,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color:
                    answerState == Answer.right
                        ? Colors.green.shade800
                        : answerState == Answer.wrong
                        ? Colors.red.shade800
                        : null,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: textController,
                    enabled: answerState == Answer.pending,
                    decoration: InputDecoration(
                      hintText: "Enter correct translation",
                      filled: true,

                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(color: Colors.grey.shade300),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: Colors.teal.shade400,
                          width: 2,
                        ),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 14,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                _buildSubmitButton(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    return Container(
      decoration: BoxDecoration(
        color: _getButtonColor(),
        borderRadius: BorderRadius.circular(12),
      ),
      child: IconButton(
        onPressed: answerState == Answer.pending ? onSubmit : null,
        icon: Icon(_getButtonIcon(), color: Colors.white),
        padding: const EdgeInsets.all(12),
      ),
    );
  }

  Color _getCardColor(BuildContext context) {
    switch (answerState) {
      case Answer.right:
        return Colors.green.shade50;
      case Answer.wrong:
        return Colors.red.shade50;
      default:
        return Theme.of(context).cardColor;
    }
  }

  Color _getBorderColor() {
    switch (answerState) {
      case Answer.right:
        return Colors.green.shade300;
      case Answer.wrong:
        return Colors.red.shade300;
      default:
        return Colors.grey.shade200;
    }
  }

  Color _getButtonColor() {
    switch (answerState) {
      case Answer.right:
        return Colors.green.shade500;
      case Answer.wrong:
        return Colors.red.shade500;
      default:
        return Colors.teal.shade500;
    }
  }

  IconData _getButtonIcon() {
    switch (answerState) {
      case Answer.right:
        return Icons.check_rounded;
      case Answer.wrong:
        return Icons.close_rounded;
      default:
        return Icons.send_rounded;
    }
  }
}
