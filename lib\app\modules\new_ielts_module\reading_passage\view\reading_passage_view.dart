import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/widget/custom_app_bar.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/book_list/controller/book_list_controller.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/book_list/widget/book_list_card.dart';

import '../controller/reading_passage_controller.dart';
import '../widget/reading_passage_card.dart';

class ReadingPassageView extends BaseView<ReadingPassageController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return CustomAppBar(
      appBarTitleText: "Passage List",
    );
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return Obx(() => ListView.builder(
        itemCount: controller.bookList.length,
        itemBuilder: (context, index) {
          var book = controller.bookList[index];
          return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(milliseconds: 800),
              child: SlideAnimation(
                  verticalOffset: 50.0,
                  child: FadeInAnimation(
                      child: ReadingPassageCard(
                    reading: book,
                  ))));
        }));
    throw UnimplementedError();
  }
}
