import 'dart:ffi';

import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';

import '../../../home/<USER>/lesson_ui_data.dart';

class StoryReadingController extends BaseController {
  var lessonUiData = LessonUiData().obs;

  @override
  void onInit() {
    var args = Get.arguments;
    if (args is LessonUiData) {
      lessonUiData.value = args;
    }
    super.onInit();
  }
}
