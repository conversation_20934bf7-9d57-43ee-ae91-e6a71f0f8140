import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/values/app_values.dart';
import '../../../core/widget/paging_view.dart';
import '../widget/leader_board_card.dart';
import '/app/core/base/base_view.dart';
import '/app/core/values/text_styles.dart';
import '/app/modules/favorite/controllers/favorite_controller.dart';

class FavoriteView extends BaseView<FavoriteController> {
  FavoriteView() {
    controller.getScoreUserList();
  }
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
  }

  @override
  Widget body(BuildContext context) {
    return PagingView(
      onRefresh: () async {
        controller.onRefreshPage();
      },
      onLoadNextPage: () {
        controller.onLoadNextPage();
      },
      child: Padding(
        padding: const EdgeInsets.all(AppValues.padding),
        child: Obx(
          () => ListView.separated(
            shrinkWrap: true,
            itemCount: controller.leaderList.length,
            primary: false,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              var model = controller.leaderList[index];

              return LeaderBoardCard(model: model, index: index);
            },
            separatorBuilder:
                (BuildContext context, int index) =>
                    const SizedBox(height: AppValues.smallMargin),
          ),
        ),
      ),
    );
  }

  Widget noLeaderFound() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset('assets/podium.png', height: 100),
        SizedBox(height: AppValues.padding),
        Text('No leader found!', style: titleStyle),
      ],
    );
  }
}
