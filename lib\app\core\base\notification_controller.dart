import 'dart:math';

import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

import '../values/new_word_data.dart';

class NotificationService {
  Future<void> _checkAndRequestNotificationPermissions() async {
    if (await Permission.notification.isDenied) {
      await Permission.notification.request();
    }

    if (await Permission.scheduleExactAlarm.isDenied) {
      await Permission.scheduleExactAlarm.request();
    }
  }

  static final NotificationService _instance = NotificationService._internal();

  factory NotificationService() {
    return _instance;
  }

  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  Future<void> init() async {
    await _checkAndRequestNotificationPermissions();
    tz.initializeTimeZones();
    SharedPreferences prefs = await SharedPreferences.getInstance();
    bool notificationsScheduled =
        prefs.getBool('notificationsScheduled') ?? false;

    String currentTimeZone;
    try {
      currentTimeZone = 'UTC';
      // currentTimeZone = await FlutterTimezone.getLocalTimezone();
      print(currentTimeZone);
    } catch (e) {
      currentTimeZone =
          'UTC'; // Fallback to UTC if the timezone cannot be determined
    }

    tz.setLocalLocation(tz.getLocation(currentTimeZone));

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    final InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
    );

    await _notificationsPlugin.initialize(
      initializationSettings,
    );

    if (!notificationsScheduled) {
      await _scheduleDailyNotification();
      await prefs.setBool('notificationsScheduled', true);
    }
  }

  Future<void> _scheduleDailyNotification() async {
    final random = Random();
    List<int> notificationHours = [
      9,
      13,
      17,
      21
    ]; // Example times for notifications

    for (int i = 0; i < notificationHours.length; i++) {
      // Randomly select a word
      final wordPair = wordList[random.nextInt(wordList.length)];
      String title = "Word of the Day: ${wordPair['answer']}";
      String message = wordPair['question'] ?? '';

      // Schedule the notification at a specific time each day
      tz.TZDateTime now = tz.TZDateTime.now(tz.local);
      tz.TZDateTime scheduledTime = tz.TZDateTime(
        tz.local,
        now.year,
        now.month,
        now.day,
        notificationHours[i],
        0,
      );
      await _notificationsPlugin.zonedSchedule(
        i, // Unique ID for each notification
        title,
        message,
        scheduledTime,
        _notificationDetails(),
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        androidAllowWhileIdle: true,
        matchDateTimeComponents: DateTimeComponents.time,
      );
    }
  }

  NotificationDetails _notificationDetails() {
    return NotificationDetails(
      android: AndroidNotificationDetails('daily_word_meanign', 'Daily Words',
          importance: Importance.max,
          priority: Priority.high,
          icon: '@drawable/icon'),
    );
  }
}
