import 'dart:math';

import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

import '../values/new_word_data.dart';

class NotificationService {
  Future<void> _checkAndRequestNotificationPermissions() async {
    if (await Permission.notification.isDenied) {
      await Permission.notification.request();
    }

    if (await Permission.scheduleExactAlarm.isDenied) {
      await Permission.scheduleExactAlarm.request();
    }
  }

  static final NotificationService _instance = NotificationService._internal();

  factory NotificationService() {
    return _instance;
  }

  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  Future<void> init() async {
    await _checkAndRequestNotificationPermissions();
    tz.initializeTimeZones();

    String currentTimeZone;
    try {
      currentTimeZone = await FlutterTimezone.getLocalTimezone();
      print(currentTimeZone);
    } catch (e) {
      currentTimeZone = 'UTC';
    }

    tz.setLocalLocation(tz.getLocation(currentTimeZone));

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    final InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);

    await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Always refresh notifications to ensure they're up to date
    await _refreshDailyNotifications();
  }

  // Handle notification tap
  void _onNotificationTapped(NotificationResponse notificationResponse) async {}

  Future<void> _refreshDailyNotifications() async {
    try {
      // Cancel existing notifications to avoid duplicates
      await _notificationsPlugin.cancelAll();

      // Schedule new notifications
      await _scheduleDynamicNotifications();

      // Save last refresh date
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'lastNotificationRefresh',
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      print('Error refreshing notifications: $e');
    }
  }

  Future<void> _scheduleDynamicNotifications() async {
    List<int> notificationHours = [9, 13, 17, 21];

    // Schedule for next 30 days to ensure continuity
    for (int day = 0; day < 30; day++) {
      for (int i = 0; i < notificationHours.length; i++) {
        try {
          // Generate unique ID for each notification
          int notificationId = (day * 10) + i;

          // Schedule the notification
          tz.TZDateTime scheduledTime = _getScheduledTime(
            notificationHours[i],
            day,
          );

          // Skip past times for today
          if (day == 0 && scheduledTime.isBefore(tz.TZDateTime.now(tz.local))) {
            continue;
          }

          await _notificationsPlugin.zonedSchedule(
            notificationId,
            'Daily Word Challenge', // Generic title
            'Tap to learn a new word!', // Generic message
            scheduledTime,
            _notificationDetails(),
            uiLocalNotificationDateInterpretation:
                UILocalNotificationDateInterpretation.absoluteTime,
            androidAllowWhileIdle: true,
            payload:
                'word_notification_$notificationId', // Use payload for word selection
          );
        } catch (e) {
          print(
            'Error scheduling notification for day $day, hour ${notificationHours[i]}: $e',
          );
        }
      }
    }
  }

  tz.TZDateTime _getScheduledTime(int hour, int daysFromNow) {
    tz.TZDateTime now = tz.TZDateTime.now(tz.local);
    tz.TZDateTime scheduledTime = tz.TZDateTime(
      tz.local,
      now.year,
      now.month,
      now.day + daysFromNow,
      hour,
      0,
    );
    return scheduledTime;
  }

  NotificationDetails _notificationDetails() {
    return NotificationDetails(
      android: AndroidNotificationDetails(
        'daily_word_meaning',
        'Daily Words',
        channelDescription: 'Daily vocabulary notifications',
        importance: Importance.max,
        priority: Priority.high,
        icon: '@drawable/icon',
        enableVibration: true,
        playSound: true,
      ),
    );
  }

  // Call this method periodically (e.g., when app opens) to refresh notifications
  Future<void> checkAndRefreshNotifications() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? lastRefreshStr = prefs.getString('lastNotificationRefresh');

    if (lastRefreshStr != null) {
      DateTime lastRefresh = DateTime.parse(lastRefreshStr);
      DateTime now = DateTime.now();

      // Refresh if it's been more than 7 days
      if (now.difference(lastRefresh).inDays >= 7) {
        await _refreshDailyNotifications();
      }
    } else {
      // First time, schedule notifications
      await _refreshDailyNotifications();
    }
  }

  // Method to cancel all notifications
  Future<void> cancelAllNotifications() async {
    await _notificationsPlugin.cancelAll();
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('lastNotificationRefresh');
  }
}

// Separate service for word selection
class WordSelectionService {
  static final Random _random = Random();

  // Get a random word safely with error handling
  static Map<String, String>? getRandomWord(
    List<Map<String, String>> wordList,
  ) {
    try {
      if (wordList.isEmpty) return null;

      return wordList[_random.nextInt(wordList.length)];
    } catch (e) {
      print('Error selecting random word: $e');
      return null;
    }
  }

  // Get word based on notification ID for consistency
  static Map<String, String>? getWordForNotification(
    List<Map<String, String>> wordList,
    int notificationId,
  ) {
    try {
      if (wordList.isEmpty) return null;

      // Use notification ID as seed for consistent but varied selection
      Random seededRandom = Random(notificationId);
      return wordList[seededRandom.nextInt(wordList.length)];
    } catch (e) {
      print('Error selecting word for notification: $e');
      return null;
    }
  }

  // Get word based on date and time for true daily variation
  static Map<String, String>? getDailyWord(
    List<Map<String, String>> wordList,
    DateTime date,
    int hourSlot,
  ) {
    try {
      if (wordList.isEmpty) return null;

      // Create seed based on date and hour slot
      int seed = date.year * 10000 + date.month * 100 + date.day + hourSlot;
      Random seededRandom = Random(seed);
      return wordList[seededRandom.nextInt(wordList.length)];
    } catch (e) {
      print('Error selecting daily word: $e');
      return null;
    }
  }
}
