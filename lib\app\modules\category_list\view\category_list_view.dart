import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/widget/custom_app_bar.dart';

import '../../../core/values/data.dart';
import '../controller/category_list_controller.dart';
import '../widget/main_category_card.dart';

class CategoryListView extends BaseView<CategoryListController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return CustomAppBar(appBarTitleText: "All Category");
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
      ),
      itemCount: mainCategory.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.all(10.0),
          child: InkWell(
            onTap: () {},
            child: MainCategoryCard(
              index: index,
              list: mainCategory,
              part: controller.part.value,
            ),
          ),
        );
      },
    );
    throw UnimplementedError();
  }
}
