import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/reading_list/model/book_data_model.dart';

import '../../../../data/model/reading_response_data.dart';
import '../../../../data/repository/lesson_repository.dart';

class ReadingListController extends BaseController {
  final RxList<ReadingListModel> _bookListController = RxList.empty();

  List<ReadingListModel> get bookList => _bookListController.toList();

  final LessonRepository _repository =
      Get.find(tag: (LessonRepository).toString());

  getBookList(int id) {
    var bookListService = _repository.getReadingListOfBook(id);
    callDataService(bookListService,
        onSuccess: _handleBookListResponseSuccess,
        onError: _handleBookListError);
  }

  _handleBookListResponseSuccess(ReadingResponseData response) {
    _bookListController.clear();
    _bookListController.addAll(response.readingList!
        .map((e) => ReadingListModel.fromData(e.toJson())));
  }

  _handleBookListError(Exception exception) {}

  @override
  void onInit() {
    var arg = Get.arguments;
    if (arg is int) {
      getBookList(arg);
    }

    super.onInit();
  }
}
