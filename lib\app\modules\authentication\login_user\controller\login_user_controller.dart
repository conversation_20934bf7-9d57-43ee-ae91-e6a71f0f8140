import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import 'package:news_lexica_app/app/data/repository/authentication_repository.dart';
import 'package:news_lexica_app/app/routes/app_pages.dart';

import '../../../../data/repository/pref_repository.dart';

class LoginUserController extends BaseController {
  final AuthenticationRepository _repository = Get.find(
    tag: (AuthenticationRepository).toString(),
  );
  final PrefRepository _pref = Get.find(tag: (PrefRepository).toString());

  var isPasswordObscured = false.obs;

  void togglePasswordVisibility() {
    isPasswordObscured.value = !isPasswordObscured.value;
  }

  var phoneController = TextEditingController();
  var passwordController = TextEditingController();
  signInUser() {
    var signInService = _repository.signInUser(
      phoneController.text,
      passwordController.text,
    );
    callDataService(
      signInService,
      onSuccess: _handleSuccess,
      onError: _handleError,
    );
  }

  _handleSuccess(Map<String, dynamic> response) {
    _saveToken(response);
    Get.toNamed(Routes.MAIN);
  }

  _saveToken(Map<String, dynamic> response) {
    _pref.setString('token', response['accessToken']);
    _pref.setString("refreshToken", response['refreshToken']);
  }

  _handleError(Exception exception) {
    Fluttertoast.showToast(msg: errorMessage);
  }
}
