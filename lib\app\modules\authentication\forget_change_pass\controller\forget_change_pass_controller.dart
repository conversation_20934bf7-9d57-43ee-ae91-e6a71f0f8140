import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import 'package:news_lexica_app/app/routes/app_pages.dart';

import '../../../../core/model/forget_pass_otp_model.dart';
import '../../../../core/model/sign_up_data.dart';
import '../../../../core/model/update_user_pass_body_prem.dart';
import '../../../../data/repository/authentication_repository.dart';

class ForgetChangePassController extends BaseController {
  var isPasswordObscured = false.obs;

  ForgetPassOtpModel signUpData = ForgetPassOtpModel(phone: '', hashedOtp: '');

  void togglePasswordVisibility() {
    isPasswordObscured.value = !isPasswordObscured.value;
  }

  var passwordController = TextEditingController();

  final AuthenticationRepository _repository = Get.find(
    tag: (AuthenticationRepository).toString(),
  );

  changePassword() {
    var updatePassService = _repository.updateUserPassword(
      UserPassBodyPrem(
        phone: "${signUpData.phone}",
        password: passwordController.text,
      ),
    );
    callDataService(
      updatePassService,
      onSuccess: _handleUpdatePassword,
      onError: _handleErrorUser,
    );
  }

  _handleUpdatePassword(Map<String, dynamic> response) {
    Fluttertoast.showToast(msg: 'Password Changed');
    Get.offAndToNamed(Routes.USER_LOGIN);
  }

  _handleErrorUser(Exception exception) {
    Fluttertoast.showToast(msg: errorMessage);
  }

  _handleOtpSendError(Exception exception) {
    showErrorMessage("Something went wrong");
  }

  @override
  void onInit() {
    var dataModel = Get.arguments;
    if (dataModel is ForgetPassOtpModel) {
      signUpData = dataModel;
    }
    super.onInit();
  }
}
