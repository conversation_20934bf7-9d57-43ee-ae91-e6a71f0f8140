import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/routes/app_pages.dart';
import 'package:news_lexica_app/app/core/model/main_category.dart';
import 'package:news_lexica_app/theme.dart';

import '../../../core/values/data.dart';

class CatagoryCard extends StatelessWidget {
  const CatagoryCard({super.key, required this.part, required this.item});
  final MainCategory item;
  final int part;
  @override
  Widget build(BuildContext context) {
    return Obx(
      () => GestureDetector(
        onTap:
            () =>
                part == 1
                    ? Get.toNamed(Routes.LESSON_CATEGORY, arguments: item)
                    : Get.toNamed(Routes.LESSON_LIST_TWO, arguments: item),
        child: Container(
          height: 120,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            gradient: LinearGradient(
              colors: isLightTheme.value ? lightColors[2] : darkColors[2],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              item.assets == ''
                  ? Container()
                  : Image.asset("${item.assets}", height: 70),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "${item.title}",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
