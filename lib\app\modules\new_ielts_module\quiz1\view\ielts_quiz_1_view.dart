import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/quiz1/controller/ielts_quiz_1_controller.dart';

class IeltsQuiz1View extends BaseView<IeltsQuiz1Controller> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment:
            CrossAxisAlignment.start, // Aligns the text to the start
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                onPressed: () {
                  closeDialog();
                },
                icon: Icon(Icons.close, size: 29),
              ),
              Center(
                child: Obx(
                  () => Text(
                    "Score ${controller.rightCorrected.length}/${controller.quizBrain.quizData.length}",
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ),
              ),
              Container(),
            ],
          ),
          Padding(
            padding: const EdgeInsets.all(16.0), // Add padding around the text
            child: Text(
              "বাম পাশের Word এর সাথে ডান পাশের সঠিক Synonym ম্যাচ করুন। প্রথমে Word ট্যাপ করুন। তারপর সঠিক Synonym ট্যাপ করুন",
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
          ),
          Row(
            children: [
              _buildColumn(
                controller.quizBrain.quizData
                    .map((e) => e["question"]!)
                    .toList(),
                true,
              ),
              _buildColumn(
                controller.quizBrain.quizData
                    .map((e) => e["synonym"]!)
                    .toList(),
                false,
              ),
            ],
          ),
        ],
      ),
    );
  }

  closeDialog() {
    return Get.dialog(
      AlertDialog(
        title: const Text('Quiz'),
        content: const Text('Are you sure you want to quit the quiz?'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text('No'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.back();
            },
            child: Text('yes'),
          ),
        ],
      ),
    );
  }

  Widget _buildColumn(List<String> words, bool isLeft) {
    return SizedBox(
      width: Get.width * .5,
      child: ListView.builder(
        physics: NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount: words.length,
        itemBuilder: (context, index) {
          return Obx(() {
            final isSelected =
                isLeft
                    ? controller.leftSelected.contains(words[index])
                    : controller.rightSelected.contains(words[index]);
            final isCorrected =
                isLeft
                    ? controller.leftCorrected.contains(words[index])
                    : controller.rightCorrected.contains(words[index]);
            final isWrong =
                isLeft
                    ? controller.leftWrong.contains(words[index])
                    : controller.rightWrong.contains(words[index]);
            return GestureDetector(
              onTap: () {
                if (isLeft) {
                  controller.selectLeft(words[index]);
                } else {
                  controller.selectRight(words[index]);
                }
              },
              child: AnimatedContainer(
                margin: EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                padding: EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color:
                      isWrong
                          ? Colors.red
                          : isCorrected
                          ? Colors.green
                          : isSelected
                          ? Colors.green
                          : Colors.blueGrey,
                  borderRadius: BorderRadius.circular(8.0),
                ),
                duration: const Duration(milliseconds: 100),
                child: Center(
                  child: Text(
                    words[index],
                    style: TextStyle(color: Colors.white, fontSize: 18),
                  ),
                ),
              ),
            );
          });
        },
      ),
    );
  }
}
