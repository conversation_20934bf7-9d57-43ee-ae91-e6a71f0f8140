import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';

import '../../../../data/model/reading_passage_details.dart';

class QuizPracticeExampleController extends BaseController {
  final RxList<IeltsReadingPassageExample> _lessonListController =
      RxList.empty();

  List<IeltsReadingPassageExample> get exampleList =>
      _lessonListController.toList();

  @override
  void onInit() {
    var arg = Get.arguments;
    if (arg is List<IeltsReadingPassageExample>) {
      _lessonListController(arg);
      super.onInit();
    }
    super.onInit();
  }
}
