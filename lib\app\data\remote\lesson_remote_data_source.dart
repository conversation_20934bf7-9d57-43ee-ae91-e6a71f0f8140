import 'dart:async';
import 'package:news_lexica_app/app/data/model/book_response_data.dart';
import 'package:news_lexica_app/app/data/model/lesson_data_details.dart';
import 'package:news_lexica_app/app/data/model/lesson_data.dart';
import 'package:news_lexica_app/app/data/model/promotion_data_response.dart';
import 'package:news_lexica_app/app/data/model/reading_passage_response_data.dart';

import '../../core/model/discussion_query_prem.dart';
import '../../core/model/lesson_discussion_body_prem.dart';
import '../../core/model/lesson_search_query_parm.dart';
import '../model/exam_quiz_list.dart';
import '../model/gre_quiz_list.dart';
import '../model/lesson_discussion_response.dart';
import '../model/reading_passage_details.dart';
import '../model/reading_response_data.dart';

abstract class LessonRemoteDataSrc {
  Future<LessonDetailsData> getLessonDataDetails(int lessonId);
  Future<ExamQuizResponseData> getExamQuizListByLesson(int lessonNumber);
  Future<GreQuizList> getGreQuizListByLesson(int lessonNumber);
  Future<LessonData> getLessonList(
      LessonSearchQueryParam lessonSearchQueryParam);
  Future<PromotionDataResponse> getAllPromotion();
  Future<LessonDiscussionResponse> getAllLessonDiscussion(
      DiscussionQueryPrem discussionQueryPrem);
  Future<Map<String, dynamic>> addLessonDiscussion(
      LessonDiscussionBodyPrem lessonDiscussionBodyPrem);

  Future<BookResponseData> getBookList();
  Future<ReadingResponseData> getReadingListOfBook(int id);
  Future<ReadingPassageResponseData> getReadingPassageOfReadingList(int id);
  Future<ReadingPassageDetails> getReadingPassageDetails(int id);
  Future<Map<String, dynamic>> voteDiscussion(String id, String voteType);
}
