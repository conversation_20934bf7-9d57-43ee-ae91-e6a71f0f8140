import 'package:get/get.dart';
import 'package:news_lexica_app/app/data/remote/authentication_remote_data_impl.dart';
import 'package:news_lexica_app/app/data/remote/authentication_remote_data_source.dart';
import 'package:news_lexica_app/app/data/remote/lesson_remote_data_impl.dart';
import 'package:news_lexica_app/app/data/remote/lesson_remote_data_source.dart';
import 'package:news_lexica_app/app/data/remote/user_remote_data_source.dart';

import '../data/remote/user_remote_data_source_impl.dart';

class RemoteSourceBindings implements Bindings {
  @override
  void dependencies() {



    Get.lazyPut<AuthenticationRemoteDataSource>(
          () => AuthenticationRemoteDataImpl(),
      tag: (AuthenticationRemoteDataSource).toString(),
        fenix: true
    );

    Get.lazyPut<LessonRemoteDataSrc>(
          () => LessonRemoteDataSrcImpl(),
      tag: (LessonRemoteDataSrc).toString(),
        fenix: true
    );

    Get.lazyPut<UserRemoteDataSrc>(
          () => UserRemoteDataSrcImpl(),
      tag: (UserRemoteDataSrc).toString(),
        fenix: true
    );
  }
}
