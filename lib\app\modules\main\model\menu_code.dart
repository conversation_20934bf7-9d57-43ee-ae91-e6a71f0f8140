import '../../../../l10n/app_localizations.dart';
import 'menu_item.dart';

enum MenuCode { FAVORITE, HOME, SETTINGS }

extension MenuCodeExtensions on MenuCode {
  BottomNavItem toBottomNavItem(AppLocalizations appLocalization) {
    switch (this) {
      case MenuCode.HOME:
        return BottomNavItem(
          navTitle: appLocalization.bottomNavHome,
          iconSvgName: "home-1-svgrepo-com.svg",
          menuCode: MenuCode.HOME,
          iconSvgNameFill: 'home-svgrepo-com.svg',
        );
      case MenuCode.FAVORITE:
        return BottomNavItem(
          navTitle: appLocalization.bottomNavFavorite,
          iconSvgName: "ic_favorite.svg",
          menuCode: MenuCode.FAVORITE,
          iconSvgNameFill: 'ic_favorite.svg',
        );
      case MenuCode.SETTINGS:
        return BottomNavItem(
          navTitle: appLocalization.bottomNavSettings,
          iconSvgName: "settings-svgrepo-com-2.svg",
          menuCode: MenuCode.SETTINGS,
          iconSvgNameFill: 'settings-svgrepo-com.svg',
        );
    }
  }
}
