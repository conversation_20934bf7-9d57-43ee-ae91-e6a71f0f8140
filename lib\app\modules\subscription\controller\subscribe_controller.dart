import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';

import '../../../data/model/user_data.dart';
import '../../../data/repository/pref_repository.dart';
import '../../../data/repository/user_repository.dart';
import '../../settings/model/user_ui_data.dart';
import '../model/subscription_ui_data.dart';

class SubscribeController extends BaseController {
  final UserRepository _repositoryUser =
      Get.find(tag: (UserRepository).toString());

  final Rx<UserUiData> _userUiDataController = Rx(UserUiData());
  UserUiData get userUiData => _userUiDataController.value;

  final Rx<SubscriptionUiData> _subscribeUiDataController =
      Rx(SubscriptionUiData());
  SubscriptionUiData get subscribeUiData => _subscribeUiDataController.value;

  void getUserData() {
    var future = _repositoryUser.getUserData("");
    callDataService(future, onSuccess: _handleSuccess, onError: _handleError);
  }

  _handleSuccess(UserData response) {
    _userUiDataController(UserUiData(
      id: response.message?.id != null ? response.message?.id! : 0,
      name: response.message?.name ?? "",
      photo: response.message?.photo ?? "",
      phone: response.message?.phone ?? '0',
      subscription: response.message?.subscriptionRequest == null
          ? false
          : response.message?.subscriptionRequest!.subscribed == false
              ? false
              : true,
    ));

    if (response.message?.subscriptionRequest != null) {
      _subscribeUiDataController(SubscriptionUiData(
          subscribed: response.message!.subscriptionRequest?.subscribed != null
              ? response.message!.subscriptionRequest!.subscribed!
              : false,
          createdAt: response.message!.subscriptionRequest?.createdAt != null
              ? response.message!.subscriptionRequest!.createdAt!
              : '',
          status: response.message!.subscriptionRequest?.status != null
              ? response.message!.subscriptionRequest!.status!
              : 'Pending',
          duration: response.message!.subscriptionRequest?.duration != null
              ? response.message!.subscriptionRequest!.duration!
              : '',
          expirationDate: addMonthsToDate(
              response.message!.subscriptionRequest!.createdAt.toString(),
              int.parse(
                  "${response.message!.subscriptionRequest!.duration!}"))));
    }
  }

  _handleError(Exception e) {}

  String addMonthsToDate(String dateString, int monthsToAdd) {
    // Parse the given date string
    DateTime date = DateTime.parse(dateString);

    // Add months
    DateTime newDate = DateTime(date.year, date.month + monthsToAdd, date.day);

    // Format the date
    String formattedDate = DateFormat('yyyy-MM-dd').format(newDate);

    return formattedDate;
  }
}
