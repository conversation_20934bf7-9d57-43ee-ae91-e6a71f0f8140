import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';

import '../../../data/model/user_data.dart';
import '../../../data/repository/user_repository.dart';

class ExamQuizListController extends BaseController {
  var isSubscribed = false.obs;

  final UserRepository _repositoryUser = Get.find(
    tag: (UserRepository).toString(),
  );

  void getUserData() {
    var future = _repositoryUser.getUserData("");
    callDataService(future, onSuccess: _handleSuccess, onError: _handleError);
  }

  _handleSuccess(UserData response) {
    response.message?.subscriptionRequest == null
        ? isSubscribed.value = false
        : response.message?.subscriptionRequest == false
        ? isSubscribed.value = false
        : isSubscribed.value = true;
  }

  _handleError(Exception exception) {}
}
