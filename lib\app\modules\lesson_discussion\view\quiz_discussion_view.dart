import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/widget/custom_app_bar.dart';

import '../../../core/values/app_values.dart';
import '../../../core/widget/paging_view.dart';
import '../controller/quiz_discussion_controller.dart';
import '../widget/comment_box.dart';
import '../widget/comment_bubble.dart';

class QuizDiscussionView extends BaseView<QuizDiscussionController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return CustomAppBar(appBarTitleText: "Discussion");
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return Column(
      children: [
        CommentBox(
          onSubmit: (String data) {
            if (controller.commentController.text.contains('@')) {
              controller.addCommentReply(data, controller.replyId);
            } else {
              controller.addQuizComment(data);
            }
          },
          controller: controller.commentController,
        ),
        Divider(),
        Row(children: []),
        Expanded(
          child: PagingView(
            onRefresh: () async {
              controller.onRefreshPage();
            },
            onLoadNextPage: () {
              controller.onLoadNextPage();
            },
            child: Obx(
              () => Padding(
                padding: const EdgeInsets.all(AppValues.padding),
                child: ListView.separated(
                  primary: false,
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: controller.commentList.length,
                  itemBuilder:
                      (context, index) => CommentCard(
                        model: controller.commentList[index],
                        isMe: false,
                      ),
                  separatorBuilder: (BuildContext context, int index) {
                    return SizedBox(height: AppValues.halfPadding);
                  },
                ),
              ),
            ),
          ),
        ),
      ],
    );
    throw UnimplementedError();
  }
}
