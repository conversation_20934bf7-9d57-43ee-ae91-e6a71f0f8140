name: news_lexica_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-namcarousel_slidere and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.2.9+50

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  audioplayers: ^5.1.0
  google_fonts: ^5.1.0
  shared_preferences: ^2.2.1
  flutter_html: ^3.0.0-beta.2
  in_app_update: ^4.2.3
  flutter_test:
    sdk: flutter

  analyzer: 5.13.0
  animations: 2.0.8
  cached_network_image: 3.3.0
  dio: 5.3.3
  font_awesome_flutter: 10.6.0
  flutter_localizations:
    sdk: flutter
  fluttertoast: ^8.2.4
  flutter_svg: 2.0.8
  get: 4.6.6
  intl: ^0.20.2
  logger: 2.0.2+1
  pretty_dio_logger: 1.3.1
  url_launcher: ^6.2.5
  carousel_slider: ^5.0.0
  flutter_spinkit: ^5.2.1
  in_app_review: ^2.0.9
  youtube_player_flutter: ^9.0.1
  dots_indicator: ^3.0.0
  flutter_local_notifications: ^17.2.1+1
  android_intent_plus: ^5.1.0
  permission_handler: ^11.3.1
  dio_cache_interceptor: ^3.5.0
  webview_flutter: ^4.8.0
  flutter_staggered_animations: ^1.1.1
  flutter_lints: ^6.0.0
  flutter_launcher_icons: ^0.14.4
  flutter_timezone: ^4.1.1


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  generate: true


  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/audio/
    - images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # Custom fonts for the application
  fonts:
    - family: BauhausStd
      fonts:
        - asset: assets/fonts/BauhausStd-Heavy.ttf
          weight: 700
    - family: SquareGame
      fonts:
        - asset: assets/fonts/Square-Game.otf
