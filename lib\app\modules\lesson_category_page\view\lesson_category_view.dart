import 'package:flutter/cupertino.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/widget/custom_app_bar.dart';
import 'package:news_lexica_app/app/modules/lesson_category_page/controller/lesson_category_controller.dart';
import 'package:news_lexica_app/app/modules/lesson_category_page/widget/quiz_card.dart';

import '../../../core/values/data.dart';

class LessonCategoryView extends BaseView<LessonCategoryController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return CustomAppBar(appBarTitleText: "${controller.mainCategory.title}");
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return Obx(
      () => ListView.builder(
        scrollDirection: Axis.vertical,
        itemCount: controller.lessonList.length,
        itemBuilder: (context, index) {
          return HomeQuizCard(
            lesson: controller.lessonList[index],
            index: index,
            colorSet: lightColors[index],
            category: "${controller.lessonList[index].category}",
            part: '',
            contentType: 0,
            isSubscribed: controller.isSubscribed.value,
          );
        },
      ),
    );

    throw UnimplementedError();
  }
}
