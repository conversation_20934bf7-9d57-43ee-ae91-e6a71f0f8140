import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/values/app_values.dart';
import 'package:news_lexica_app/app/data/model/lesson_data.dart';
import 'package:news_lexica_app/app/data/model/promotion_data_response.dart';
import 'package:news_lexica_app/app/data/repository/lesson_repository.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import '../../../core/model/lesson_search_query_parm.dart';
import '../model/lesson_ui_data.dart';
import '../model/promotion_ui_data.dart';
import '/app/core/base/base_controller.dart';
import '/app/core/base/paging_controller.dart';
import '/app/modules/home/<USER>/github_project_ui_data.dart';
import 'dart:convert';
import 'dart:io';

class HomeController extends BaseController {
  String getAndroidVersion() {
    if (Platform.isAndroid) {
      String version = Platform.operatingSystemVersion; // Example: "Android 9"
      RegExp regex = RegExp(r'Android (\d+)');
      Match? match = regex.firstMatch(version);
      if (match != null) {
        return match.group(1) ?? "0"; // Extracts "9" from "Android 9"
      }
    }
    return "0"; // Default if unknown
  }

  bool isOldDevice() {
    int androidVersion = int.parse(getAndroidVersion());
    return androidVersion < 10; // Customize threshold
  }

  var isOlderDevice = false.obs;

  final RxList<PromotionUiData> _promotionListController = RxList.empty();

  List<PromotionUiData> get promotionList => _promotionListController.toList();

  var bannerPosition = 0.obs;
  final LessonRepository _repository = Get.find(
    tag: (LessonRepository).toString(),
  );

  final RxList<LessonUiData> _lessonListController = RxList.empty();

  List<LessonUiData> get lessonList => _lessonListController.toList();

  final pagingController = PagingController<GithubProjectUiData>();

  void getLessonList() {
    var queryParam = LessonSearchQueryParam(contentType: 0);

    var lessonSearchService = _repository.getLessonList(queryParam);

    callDataService(
      lessonSearchService,
      onSuccess: _handleLessonListResponseSuccess,
      onError: _handleLessonGetError,
    );
  }

  onRefreshPage() {
    pagingController.initRefresh();
  }

  onLoadNextPage() {
    logger.i("On load next");
  }

  void getAllPromotion() {
    var serviceFuture = _repository.getAllPromotion();
    callDataService(serviceFuture, onSuccess: _handlePromotionSuccess);
  }

  Future<void> readJson() async {
    final String response = await rootBundle.loadString(
      'assets/free_lesson.json',
    );
    final data = await json.decode(response);
    var freeLesson = LessonData.fromJson(data);
    _handleLessonListResponseSuccess(freeLesson);
  }

  void _handleLessonListResponseSuccess(LessonData response) {
    List<LessonUiData>? repoList =
        response.message
            ?.map(
              (e) => LessonUiData(
                pressName: e.pressName != null ? e.pressName! : "Null",
                title: e.title != null ? e.title! : "Null",
                category: e.category != null ? e.category! : "Null",
                id: e.id != null ? e.id! : 0,
              ),
            )
            .toList();

    if (repoList != null) {
      repoList.sort((a, b) => a.lessonNumber!.compareTo(b.lessonNumber!));
    }

    _lessonListController(repoList);
  }

  YoutubePlayerController myController = YoutubePlayerController(
    initialVideoId: 'RwmwVwcCsWs',
    flags: YoutubePlayerFlags(autoPlay: true, mute: false),
  );

  _handlePromotionSuccess(PromotionDataResponse response) {
    List<PromotionUiData>? repoList =
        response.message
            ?.map(
              (e) => PromotionUiData(
                name: e.name != null ? e.name! : "Null",
                imageSrc: e.photo != null ? e.photo! : "Null",
                id: e.id != null ? e.id! : 0,
              ),
            )
            .toList();
    _promotionListController(repoList);
  }

  _handleLessonGetError(Exception exception) {
    Fluttertoast.showToast(msg: errorMessage);
  }

  @override
  void onInit() {
    isOlderDevice(isOldDevice());
    super.onInit();
  }
}
