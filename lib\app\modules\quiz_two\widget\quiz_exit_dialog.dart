import 'package:flutter/material.dart';
import 'package:get/get.dart';

class QuizExitDialog extends StatelessWidget {
  final VoidCallback onConfirmExit;

  const QuizExitDialog({Key? key, required this.onConfirmExit})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Row(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            color: Colors.orange.shade600,
            size: 24,
          ),
          const SizedBox(width: 12),
          const Text('Exit Quiz'),
        ],
      ),
      content: const Text('Are you sure you want to quit the quiz?'),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: Text('Cancel', style: TextStyle(color: Colors.grey.shade600)),
        ),
        TextButton(
          onPressed: onConfirmExit,
          style: TextButton.styleFrom(
            backgroundColor: Colors.red.shade600,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: const Text('Exit'),
        ),
      ],
    );
  }
}
