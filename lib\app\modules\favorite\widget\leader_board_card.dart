import 'package:flutter/material.dart';

import '../../../core/values/app_values.dart';
import '../model/leader_ui_data.dart';

class LeaderBoardCard extends StatelessWidget {
  const LeaderBoardCard({
    super.key,
    required this.model,
    required this.index,
  });

  final LeaderUiData model;
  final int index;
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppValues.halfPadding),
        child: ListTile(
          leading: CircleAvatar(
           radius: 25,
              backgroundColor: Colors.white,
              child:
                  Text("${index + 1}", style: TextStyle(color: Colors.black,fontSize: 14))),
          title: Text(
            model.name,
            style: TextStyle(
                color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          trailing: Wrap(
            crossAxisAlignment: WrapCrossAlignment.center,
            children: [
              index == 0
                  ? Image.asset('assets/gold.png',
                      height: AppValues.iconSize_20, width: AppValues.iconSize_20)
                  : index == 1
                      ? Image.asset('assets/silver.png',
                          height: AppValues.iconSize_20,
                          width: AppValues.iconSize_20)
                      : index == 2
                          ? Image.asset('assets/bronze.png',
                              height: AppValues.iconSize_20,
                              width: AppValues.iconSize_20)
                          :Text('') ,
              Text(
                model.score,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
