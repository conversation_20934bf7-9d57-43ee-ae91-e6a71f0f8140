import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../model/leader_ui_data.dart';

class LeaderBoardCard extends StatelessWidget {
  const LeaderBoardCard({super.key, required this.model, required this.index});

  final LeaderUiData model;
  final int index;

  @override
  Widget build(BuildContext context) {
    final bool isTopThree = index < 3;
    final bool isFirst = index == 0;

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: 16,
        vertical: isTopThree ? 8 : 4,
      ),
      child: AnimatedContainer(
        duration: Duration(milliseconds: 300),
        curve: Curves.easeOutBack,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(isTopThree ? 20 : 16),
          gradient: _getGradient(context),
          boxShadow: [
            BoxShadow(
              color: _getShadowColor(context),
              blurRadius: isTopThree ? 15 : 8,
              offset: Offset(0, isTopThree ? 8 : 4),
              spreadRadius: isTopThree ? 2 : 0,
            ),
          ],
          border:
              isFirst
                  ? Border.all(color: Colors.amber.withOpacity(0.5), width: 2)
                  : null,
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(isTopThree ? 20 : 16),
            onTap: () {
              // Add haptic feedback
              HapticFeedback.lightImpact();
              // You can add navigation or detail view here
            },
            child: Padding(
              padding: EdgeInsets.all(isTopThree ? 20 : 16),
              child: Row(
                children: [
                  // Rank Badge
                  _buildRankBadge(context),

                  const SizedBox(width: 16),

                  // User Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          model.name,
                          style: Theme.of(
                            context,
                          ).textTheme.titleMedium?.copyWith(
                            color: _getTextColor(context),
                            fontWeight:
                                isTopThree ? FontWeight.bold : FontWeight.w600,
                            fontSize: isTopThree ? 18 : 16,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (isTopThree) ...[
                          const SizedBox(height: 4),
                          Text(
                            _getRankText(),
                            style: Theme.of(
                              context,
                            ).textTheme.bodySmall?.copyWith(
                              color: _getTextColor(context).withOpacity(0.8),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  // Medal and Score
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (isTopThree) ...[
                        _buildMedalWidget(),
                        const SizedBox(width: 12),
                      ],

                      // Score Container
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(
                            isTopThree ? 0.25 : 0.15,
                          ),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          model.score,
                          style: TextStyle(
                            color: _getTextColor(context),
                            fontSize: isTopThree ? 16 : 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRankBadge(BuildContext context) {
    final bool isTopThree = index < 3;
    final size = isTopThree ? 50.0 : 40.0;

    return Hero(
      tag: 'rank_${index}',
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: _getRankGradient(),
          boxShadow: [
            BoxShadow(
              color: _getRankShadowColor(),
              blurRadius: isTopThree ? 10 : 6,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(color: Colors.white.withOpacity(0.3), width: 2),
        ),
        child: Center(
          child: Text(
            "${index + 1}",
            style: TextStyle(
              color: Colors.white,
              fontSize: isTopThree ? 20 : 16,
              fontWeight: FontWeight.bold,
              shadows: [
                Shadow(
                  color: Colors.black.withOpacity(0.3),
                  offset: const Offset(1, 1),
                  blurRadius: 2,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMedalWidget() {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white.withOpacity(0.2),
        border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
      ),
      child: Center(child: _getMedalIcon()),
    );
  }

  Widget _getMedalIcon() {
    switch (index) {
      case 0:
        return Icon(Icons.emoji_events, color: Colors.amber, size: 20);
      case 1:
        return Icon(Icons.emoji_events, color: Colors.grey[300], size: 20);
      case 2:
        return Icon(Icons.emoji_events, color: Colors.brown[300], size: 20);
      default:
        return const SizedBox();
    }
  }

  LinearGradient _getGradient(BuildContext context) {
    switch (index) {
      case 0:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.amber.shade400,
            Colors.amber.shade600,
            Colors.amber.shade800,
          ],
          stops: const [0.0, 0.5, 1.0],
        );
      case 1:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.grey.shade400,
            Colors.grey.shade500,
            Colors.grey.shade600,
          ],
        );
      case 2:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.brown.shade400,
            Colors.brown.shade500,
            Colors.brown.shade600,
          ],
        );
      default:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).primaryColor.withOpacity(0.1),
            Theme.of(context).primaryColor.withOpacity(0.05),
          ],
        );
    }
  }

  LinearGradient _getRankGradient() {
    switch (index) {
      case 0:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.amber.shade300,
            Colors.amber.shade500,
            Colors.amber.shade700,
          ],
        );
      case 1:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.grey.shade300,
            Colors.grey.shade400,
            Colors.grey.shade500,
          ],
        );
      case 2:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.brown.shade300,
            Colors.brown.shade400,
            Colors.brown.shade500,
          ],
        );
      default:
        return LinearGradient(
          colors: [Colors.blueGrey.shade400, Colors.blueGrey.shade600],
        );
    }
  }

  Color _getShadowColor(BuildContext context) {
    switch (index) {
      case 0:
        return Colors.amber.withOpacity(0.4);
      case 1:
        return Colors.grey.withOpacity(0.3);
      case 2:
        return Colors.brown.withOpacity(0.3);
      default:
        return Theme.of(context).shadowColor.withOpacity(0.15);
    }
  }

  Color _getRankShadowColor() {
    switch (index) {
      case 0:
        return Colors.amber.withOpacity(0.5);
      case 1:
        return Colors.grey.withOpacity(0.4);
      case 2:
        return Colors.brown.withOpacity(0.4);
      default:
        return Colors.blueGrey.withOpacity(0.3);
    }
  }

  Color _getTextColor(context) {
    return index < 3
        ? Colors.white
        : Theme.of(context).textTheme.titleMedium?.color ?? Colors.black;
  }

  String _getRankText() {
    switch (index) {
      case 0:
        return "🏆 Champion";
      case 1:
        return "🥈 Runner Up";
      case 2:
        return "🥉 Third Place";
      default:
        return "";
    }
  }
}
