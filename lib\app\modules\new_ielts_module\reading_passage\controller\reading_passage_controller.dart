import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import '../../../../data/model/reading_passage_response_data.dart';
import '../../../../data/model/reading_response_data.dart';
import '../../../../data/repository/lesson_repository.dart';
import '../model/reading_passage_model.dart';

class ReadingPassageController extends BaseController {
  final RxList<ReadingPassageModel> _bookListController = RxList.empty();

  List<ReadingPassageModel> get bookList => _bookListController.toList();
  final LessonRepository _repository =
      Get.find(tag: (LessonRepository).toString());

  getBookList(int id) {
    var bookListService = _repository.getReadingPassageOfReadingList(id);
    callDataService(bookListService,
        onSuccess: _handleBookListResponseSuccess,
        onError: _handleBookListError);
  }

  _handleBookListResponseSuccess(ReadingPassageResponseData response) {
    _bookListController.clear();
    _bookListController.addAll(response.passageList!
        .map((e) => ReadingPassageModel.fromData(e.toJson())));
  }

  _handleBookListError(Exception exception) {}

  @override
  void onInit() {
    var arg = Get.arguments;
    if (arg is int) {
      getBookList(arg);
    }

    super.onInit();
  }
}
