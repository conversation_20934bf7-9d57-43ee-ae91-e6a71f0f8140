import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/values/app_values.dart';
import 'package:news_lexica_app/app/core/values/text_styles.dart';
import 'package:news_lexica_app/app/core/widget/custom_app_bar.dart';
import 'package:news_lexica_app/app/preface_page.dart';

class LexicaAboutPage extends StatelessWidget {
  const LexicaAboutPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(appBarTitleText: 'Author'),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: SingleChildScrollView(
          child: Column(
            children: [
              Text(
                "NewsLexica",
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.purpleAccent, fontWeight: FontWeight.bold),
              ),
              Text(
                "A Practical Guide to Understanding Newspaper and Mastering Vocabulary",
                style: Theme.of(context)
                    .textTheme
                    .titleMedium!
                    .copyWith(color: Colors.blue),
              ),
              SizedBox(
                height: 20,
              ),
              Text(
                "(ইংরেজি পত্রিকা বুঝা ও ভোকাবিউলারি আয়ত্ত্ব করার অনুশীলন বই)",
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                    onPressed: () {
                      Get.to(() => PrefacePage());
                    },
                    child: Text(
                      "Preface",
                      style: TextStyle(fontSize: 18),
                    )),
              ),
              CircleAvatar(
                  backgroundImage: AssetImage("assets/writer.png"), radius: 60),
              Text("Author And Designed By"),
              Text("Munir Hussain",
                  style: Theme.of(context).textTheme.titleLarge),
              SizedBox(
                height: 5,
              ),
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  'Education',
                  style: titleStyle,
                ),
              ),
              ListTile(
                leading: CircleAvatar(
                  child: Image.network(
                      'https://upload.wikimedia.org/wikipedia/en/thumb/8/86/University_of_Chittagong_logo.svg/220px-University_of_Chittagong_logo.svg.png'),
                ),
                title: Text(
                  "University of Chittagong",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: Text("BA (Honours), MA (English)"),
              ),
              Divider(
                color: Colors.grey,
              ),
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  'Work',
                  style: titleStyle,
                ),
              ),
              ListTile(
                leading: CircleAvatar(
                  child: Image.network(
                      'https://upload.wikimedia.org/wikipedia/commons/thumb/8/84/Government_Seal_of_Bangladesh.svg/120px-Government_Seal_of_Bangladesh.svg.png'),
                ),
                title: Text("Ministry of Social Welfare",
                    style: TextStyle(fontWeight: FontWeight.bold)),
                subtitle: Text("Upazila Social Services Officer"),
              ),
              SizedBox(
                height: 5,
              ),
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  'Contact',
                  style: titleStyle,
                ),
              ),
              ListTile(
                leading: CircleAvatar(
                  child: Icon(Icons.email),
                ),
                title: Text(
                  "Email",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: Text("<EMAIL>"),
              ),
              ListTile(
                leading: CircleAvatar(
                  child: Icon(Icons.message),
                ),
                title: Text(
                  "WhatsApp",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: Text("01860507913"),
              ),
              SizedBox(
                height: AppValues.padding,
              ),
              Align(
                alignment: Alignment.bottomLeft,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text("Gratitude To",
                        style: Theme.of(context).textTheme.bodySmall),
                    Text("Shihab Hossain (App Development)",
                        style: Theme.of(context).textTheme.bodySmall),
                    Text("Monagharian Kathbirali (Writing assistance)",
                        style: Theme.of(context).textTheme.bodySmall),
                  ],
                ),
              ),
              SizedBox(
                height: 10,
              )
            ],
          ),
        ),
      ),
    );
  }
}
