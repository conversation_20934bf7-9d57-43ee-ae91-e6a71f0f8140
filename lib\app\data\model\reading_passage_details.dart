class ReadingPassageDetails {
  int? id;
  String? name;
  int? ieltsRadingListId;
  String? createdAt;
  String? updatedAt;
  List<IeltsReadingPassageQuizOne>? ieltsReadingPassageQuizOne;
  List<IeltsReadingPassageQuizTwo>? ieltsReadingPassageQuizTwo;
  List<Passage>? passage;
  List<IeltsReadingPassageExample>? ieltsReadingPassageExample;

  ReadingPassageDetails(
      {this.id,
      this.name,
      this.ieltsRadingListId,
      this.createdAt,
      this.updatedAt,
      this.ieltsReadingPassageQuizOne,
      this.ieltsReadingPassageQuizTwo,
      this.passage,
      this.ieltsReadingPassageExample});

  ReadingPassageDetails.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    ieltsRadingListId = json['ieltsRadingListId'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    if (json['IeltsReadingPassageQuizOne'] != null) {
      ieltsReadingPassageQuizOne = <IeltsReadingPassageQuizOne>[];
      json['IeltsReadingPassageQuizOne'].forEach((v) {
        ieltsReadingPassageQuizOne!
            .add(new IeltsReadingPassageQuizOne.fromJson(v));
      });
    }
    if (json['IeltsReadingPassageQuizTwo'] != null) {
      ieltsReadingPassageQuizTwo = <IeltsReadingPassageQuizTwo>[];
      json['IeltsReadingPassageQuizTwo'].forEach((v) {
        ieltsReadingPassageQuizTwo!
            .add(new IeltsReadingPassageQuizTwo.fromJson(v));
      });
    }
    if (json['Passage'] != null) {
      passage = <Passage>[];
      json['Passage'].forEach((v) {
        passage!.add(new Passage.fromJson(v));
      });
    }

    if (json['IeltsReadingPassageExample'] != null) {
      ieltsReadingPassageExample = <IeltsReadingPassageExample>[];
      json['IeltsReadingPassageExample'].forEach((v) {
        ieltsReadingPassageExample!
            .add(new IeltsReadingPassageExample.fromJson(v));
      });
    }
  }
}

class IeltsReadingPassageQuizOne {
  int? id;
  String? question;
  String? option1;
  String? answer;
  int? ieltsReadingPassageId;

  IeltsReadingPassageQuizOne(
      {this.id,
      this.question,
      this.option1,
      this.answer,
      this.ieltsReadingPassageId});

  IeltsReadingPassageQuizOne.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    question = json['question'];
    option1 = json['option1'];
    answer = json['answer'];
    ieltsReadingPassageId = json['ieltsReadingPassageId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['question'] = this.question;
    data['option1'] = this.option1;
    data['answer'] = this.answer;
    data['ieltsReadingPassageId'] = this.ieltsReadingPassageId;
    return data;
  }
}

class IeltsReadingPassageQuizTwo {
  int? id;
  String? question;
  String? option1;
  String? answer;
  int? ieltsReadingPassageId;

  IeltsReadingPassageQuizTwo(
      {this.id,
      this.question,
      this.option1,
      this.answer,
      this.ieltsReadingPassageId});

  IeltsReadingPassageQuizTwo.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    question = json['question'];
    option1 = json['option1'];
    answer = json['answer'];
    ieltsReadingPassageId = json['ieltsReadingPassageId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['question'] = this.question;
    data['option1'] = this.option1;
    data['answer'] = this.answer;
    data['ieltsReadingPassageId'] = this.ieltsReadingPassageId;
    return data;
  }
}

class Passage {
  int? id;
  String? title;
  String? paragraph;
  int? ieltsReadingPassageId;
  String? createdAt;
  String? updatedAt;

  Passage(
      {this.id,
      this.title,
      this.paragraph,
      this.ieltsReadingPassageId,
      this.createdAt,
      this.updatedAt});

  Passage.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    paragraph = json['paragraph'];
    ieltsReadingPassageId = json['ieltsReadingPassageId'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['title'] = this.title;
    data['paragraph'] = this.paragraph;
    data['ieltsReadingPassageId'] = this.ieltsReadingPassageId;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    return data;
  }
}

class IeltsReadingPassageExample {
  int? id;
  String? word;
  String? synonym;
  String? antonym;
  int? ieltsReadingPassageId;

  IeltsReadingPassageExample(
      {this.id,
      this.word,
      this.synonym,
      this.antonym,
      this.ieltsReadingPassageId});

  IeltsReadingPassageExample.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    word = json['word'];
    synonym = json['synonym'];
    antonym = json['antonym'];
    ieltsReadingPassageId = json['ieltsReadingPassageId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['word'] = this.word;
    data['synonym'] = this.synonym;
    data['antonym'] = this.antonym;
    data['ieltsReadingPassageId'] = this.ieltsReadingPassageId;
    return data;
  }
}
