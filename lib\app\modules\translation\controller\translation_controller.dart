import 'package:news_lexica_app/app/core/base/base_controller.dart';
import 'package:webview_flutter/webview_flutter.dart';

class TranslationController extends BaseController {
  var webController = WebViewController()
    ..setJavaScriptMode(JavaScriptMode.unrestricted)
    ..setNavigationDelegate(
      NavigationDelegate(
        onProgress: (int progress) {
          // Update loading bar.
        },
        onPageStarted: (String url) {},
        onPageFinished: (String url) {},
        onHttpError: (HttpResponseError error) {},
        onWebResourceError: (WebResourceError error) {},
        onNavigationRequest: (NavigationRequest request) {
          if (request.url.startsWith(
              'https://translate.google.com/?sl=en&tl=bn&op=translate')) {
            return NavigationDecision.prevent;
          }
          return NavigationDecision.prevent;
        },
      ),
    )
    ..loadRequest(
        Uri.parse('https://translate.google.com/?sl=en&tl=bn&op=translate'));
}
