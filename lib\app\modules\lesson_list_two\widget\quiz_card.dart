import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/modules/home/<USER>/lesson_ui_data.dart';

import '../../../routes/app_pages.dart';

class HomeQuizCard extends StatelessWidget {
  const HomeQuizCard({
    super.key,
    required this.lesson,
    required this.index,
    required this.colorSet,
    required this.category,
    required this.part,
    required this.contentType,
    required this.isSubscribed,
  });
  final LessonUiData lesson;
  final String category;
  final String part;
  final int index;
  final List<Color> colorSet;
  final int contentType;
  final bool isSubscribed;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (isSubscribed) {
          Get.toNamed(Routes.LESSON_DETAILS, arguments: lesson.id);
        } else {
          subscribeDialog();
        }
        //contentType==1? demoUser.subscribe==0? subscribeDialog():Get.to(() =>  LessonPage(lesson: lesson[index],), transition: Transition.upToDown):Get.to(() =>  LessonPage(lesson: lesson[index],), transition: Transition.upToDown);
      },
      child: Container(
        width: double.infinity,
        child: Stack(
          children: [
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: colorSet,
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
                borderRadius: BorderRadius.circular(15.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 8.0,
                    offset: Offset(0.0, 5.0),
                  ),
                ],
              ),
              margin: EdgeInsets.all(10),
              child: Padding(
                padding: const EdgeInsets.all(15.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    isSubscribed
                        ? Wrap(
                          crossAxisAlignment: WrapCrossAlignment.center,
                          children: [
                            Wrap(
                              children: [
                                Column(
                                  children: [
                                    SizedBox(height: 5),
                                    CircularProgressIndicator(
                                      value: .0,
                                      backgroundColor: Colors.grey,
                                    ),
                                    SizedBox(width: 10),
                                    Text("0%"),
                                  ],
                                ),
                                Text(
                                  "$category",
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                    fontSize: 18,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        )
                        : Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Wrap(
                              crossAxisAlignment: WrapCrossAlignment.center,
                              children: [
                                CircleAvatar(
                                  radius: 20,
                                  child: Icon(Icons.lock),
                                ),
                                SizedBox(width: 10),
                                Text(
                                  "$category",
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                    fontSize: 18,
                                  ),
                                ),
                              ],
                            ),
                            Text(
                              "$part",
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                fontSize: 18,
                              ),
                            ),
                          ],
                        ),
                    const SizedBox(height: 5),
                    Text(
                      'Story ${index + 1}',
                      style: TextStyle(color: Colors.white),
                    ),
                    const SizedBox(height: 5),
                    RichText(
                      text: TextSpan(
                        text: '${lesson.title}',

                        style: TextStyle(color: Colors.white, fontSize: 16),
                        children: <TextSpan>[
                          TextSpan(
                            text: '${lesson.pressName}',
                            style: TextStyle(
                              fontStyle: FontStyle.italic,
                              color: Colors.blue,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 5),
                  ],
                ),
              ),
            ),
            Positioned(
              right: 15,
              child: Padding(
                padding: const EdgeInsets.only(top: 0, right: 60),
                child:
                    lesson.picture == "Null"
                        ? null
                        : Image.network("${lesson.picture}", height: 80),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void subscribeDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Subscribe'),
        content: const Text(
          'You need subscription for viewing this content. Head back to our website for subscription',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text('Okay'),
          ),
        ],
      ),
    );
  }
}
