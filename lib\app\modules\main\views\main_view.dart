import 'package:animations/animations.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:news_lexica_app/app/routes/app_pages.dart';
import '../../../core/widget/home_app_bar.dart';
import '../../../lexica_about_page.dart';
import '../../../preface_page.dart';
import '/app/core/base/base_view.dart';
import '/app/modules/favorite/views/favorite_view.dart';
import '/app/modules/home/<USER>/home_view.dart';
import '/app/modules/main/controllers/main_controller.dart';
import '/app/modules/main/model/menu_code.dart';
import '/app/modules/main/views/bottom_nav_bar.dart';
import '/app/modules/other/views/other_view.dart';
import '/app/modules/settings/views/settings_view.dart';

// ignore: must_be_immutable
class MainView extends BaseView<MainController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) => HomeAppBar();

  @override
  Widget? drawer(context) {
    return Drawer(
      child: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              width: double.infinity,
              color: Colors.black87,
              child: DrawerHeader(
                decoration: BoxDecoration(
                  color: Colors.black87,
                  border: Border(bottom: BorderSide(color: Colors.black87)),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset('assets/lexica_top_icon.png', height: 25),
                        SizedBox(width: 5),
                        Text(
                          'NEWS',
                          style: GoogleFonts.poppins(
                            fontWeight: FontWeight.bold,
                            color: Color(0xff2734A1),
                          ),
                        ),
                        SizedBox(width: 2),
                        Text(
                          'LEXICA',
                          style: GoogleFonts.poppins(
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      "Your Vocabulary Trainer",
                      style: Theme.of(
                        context,
                      ).textTheme.titleSmall!.copyWith(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),
            ListTile(
              onTap: () => Get.to(() => LexicaAboutPage()),
              leading: Image.asset(
                'assets/book_writer.png',
                height: 20,
                width: 20,
              ),
              title: Text("Author"),
              trailing: Icon(Icons.chevron_right),
            ),
            ListTile(
              onTap: () => Get.toNamed(Routes.INTRO_VIDEO),
              leading: Image.asset('assets/intro.png', height: 20, width: 20),
              title: Text("Intro video"),
              trailing: Icon(Icons.chevron_right),
            ),
            ListTile(
              onTap: () => Get.to(() => PrefacePage()),
              leading: Image.asset('assets/about.png', height: 20, width: 20),
              title: Text("About This App"),
              trailing: Icon(Icons.chevron_right),
            ),
            ListTile(
              onTap: () => Get.toNamed(Routes.GRE_QUIZ_LIST),
              leading: Image.asset(
                'assets/gre_exam.png',
                height: 20,
                width: 20,
              ),
              title: Text("GRE, GMAT Vocabulary"),
              trailing: Icon(Icons.chevron_right),
            ),
            ListTile(
              onTap: () => Get.toNamed(Routes.EXAM_QUIZ_LIST),
              leading: Image.asset('assets/exam.png', height: 20, width: 20),
              title: Text("Job Exams & Admission Test Vocabulary"),
              trailing: Icon(Icons.chevron_right),
            ),
            ListTile(
              onTap: () => Get.toNamed(Routes.NEW_IELTS_BOOK_LIST),
              leading: Image.asset('assets/ielts.png', height: 20, width: 20),
              title: Text("Cambridge IELTS Vocabulary"),
              trailing: Icon(Icons.chevron_right),
            ),
            ListTile(
              onTap: () => Get.toNamed(Routes.TRANSLATION_WORDS),
              leading: Image.asset(
                'assets/translate.png',
                height: 20,
                width: 20,
              ),
              title: Text("Translate"),
              trailing: Icon(Icons.chevron_right),
            ),
            ListTile(
              onTap: () => Get.toNamed(Routes.NEWS_PAPER_LIST),
              leading: Image.asset(
                'assets/newspaper.png',
                height: 20,
                width: 20,
              ),
              title: Text("Read Global News Papers & Magazines"),
              trailing: Icon(Icons.chevron_right),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget body(BuildContext context) {
    return Container(
      key: UniqueKey(),
      child: Obx(
        () => PageTransitionSwitcher(
          transitionBuilder: (
            Widget child,
            Animation<double> animation,
            Animation<double> secondaryAnimation,
          ) {
            return FadeThroughTransition(
              animation: animation,
              secondaryAnimation: secondaryAnimation,
              child: child,
            );
          },
          child: getPageOnSelectedMenu(controller.selectedMenuCode),
        ),
      ),
    );
  }

  @override
  Widget? bottomNavigationBar() {
    return BottomNavBar(onItemSelected: controller.onMenuSelected);
  }

  final HomeView homeView = HomeView();
  FavoriteView? favoriteView;
  SettingsView? settingsView;

  Widget getPageOnSelectedMenu(MenuCode menuCode) {
    switch (menuCode) {
      case MenuCode.HOME:
        return homeView;
      case MenuCode.FAVORITE:
        favoriteView ??= FavoriteView();
        return favoriteView!;
      case MenuCode.SETTINGS:
        settingsView ??= SettingsView();
        return settingsView!;
      default:
        return OtherView(viewParam: describeEnum(menuCode));
    }
  }
}
