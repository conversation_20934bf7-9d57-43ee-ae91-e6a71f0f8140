import 'package:flutter/material.dart';
import 'package:get/get.dart';

class QuizCompletionDialog extends StatelessWidget {
  final int score;
  final int totalQuestions;
  final bool hasWrongAnswers;
  final VoidCallback onOkPressed;

  const QuizCompletionDialog({
    Key? key,
    required this.score,
    required this.totalQuestions,
    required this.hasWrongAnswers,
    required this.onOkPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.green.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.emoji_events_rounded,
              color: Colors.green.shade600,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Text('Your Score: $score/$totalQuestions'),
        ],
      ),
      content: SizedBox(
        height: Get.height * 0.25,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "Congratulations 👏",
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.green.shade700,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              "You completed the quiz.",
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            if (hasWrongAnswers) ...[
              const SizedBox(height: 16),
              Text(
                "Take the quiz again to correct your mistakes.",
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.orange.shade700),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: onOkPressed,
          style: TextButton.styleFrom(
            backgroundColor: Colors.teal.shade600,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: const Text('OK'),
        ),
      ],
    );
  }
}
