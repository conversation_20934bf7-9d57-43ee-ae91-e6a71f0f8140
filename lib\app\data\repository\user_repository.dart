import '../../core/model/score_query_prem.dart';
import '../../core/model/update_user_pass_body_prem.dart';
import '../../core/model/update_user_score_body_prem.dart';
import '../model/score_board_data_response.dart';
import '../model/user_data.dart';

abstract class UserRepository {
  Future<UserData> getUserData(String userId);
  Future<ScoreBoardDataResponse>getScoreBoard(ScoreQueryPrem scoreQueryPrem);
  Future<Map<String,dynamic>>updateUserScore(UpdateUserScoreBodyPrem updateUserScoreBodyPrem);
  Future<Map<String,dynamic>>getUserScore(String userId);

}