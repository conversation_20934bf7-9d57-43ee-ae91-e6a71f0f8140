import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';

import '../../../data/model/lesson_data.dart';
import '../../home/<USER>/lesson_ui_data.dart';

class FreeLessonController extends BaseController {
  final RxList<LessonUiData> _lessonListController = RxList.empty();

  List<LessonUiData> get lessonList => _lessonListController.toList();

  Future<void> readJson() async {
    final String response = await rootBundle.loadString(
      'assets/free_lesson.json',
    );
    final data = await json.decode(response);
    var freeLesson = LessonData.fromJson(data);
    _handleLessonListResponseSuccess(freeLesson);
  }

  void _handleLessonListResponseSuccess(LessonData response) {
    List<LessonUiData>? repoList =
        response.message
            ?.map(
              (e) => LessonUiData(
                pressName: e.pressName != null ? e.pressName! : "Null",
                title: e.title != null ? e.title! : "Null",
                lessonNumber: e.lessonNumber != null ? e.lessonNumber! : 0,
                category: e.category != null ? e.category! : "Null",
                id: e.id != null ? e.id! : 0,
              ),
            )
            .toList();

    if (repoList != null) {
      repoList.sort((a, b) => a.lessonNumber!.compareTo(b.lessonNumber!));
    }

    _lessonListController(repoList);
  }

  @override
  void onInit() {
    readJson();
    // TODO: implement onInit
    super.onInit();
  }
}
