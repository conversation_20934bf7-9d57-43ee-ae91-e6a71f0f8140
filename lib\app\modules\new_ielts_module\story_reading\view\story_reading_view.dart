import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/values/app_values.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/story_reading/controller/story_reading_controller.dart';

import '../../../../core/utils/api.dart';

class StoryReadingView extends BaseView<StoryReadingController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
        child: Padding(
      padding: const EdgeInsets.all(8.0),
      child: <PERSON>umn(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: AppValues.padding),
            child: Text(
              "${controller.lessonUiData.value.lessonTitle}",
              style: Theme.of(context).textTheme.titleLarge!.copyWith(
                  color: Colors.purple,
                  fontWeight: FontWeight.bold,
                  fontSize: 32),
            ),
          ),
          Card(
            color: Colors.blue,
            child: Padding(
              padding: const EdgeInsets.all(5),
              child: Text(
                'উচ্চারণ শুনতে ও বিস্তারিত জানতে আন্ডারলাইণ্ড ওয়ার্ড ট্যাপ করুন',
                style:
                    TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          SizedBox(height: 8),
          Html(
            data: controller.lessonUiData.value.paragraph,
            onLinkTap: (String? url, Map<String, String> attributes, element) {
              Api().launchURL(url!);
            },
          ),
          SizedBox(
            height: 50,
          ),
          Center(
            child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                    shape: BeveledRectangleBorder(
                        borderRadius: BorderRadius.circular(8)),
                    backgroundColor: Colors.teal,
                    minimumSize: Size(Get.width * .9, 50)),
                onPressed: () {
                  Get.back();
                },
                child: Text(
                  "এখন দেখুন উপরের কতটি Word আপনার মনে আছে",
                  style: TextStyle(color: Colors.white),
                )),
          )
        ],
      ),
    ));
    throw UnimplementedError();
  }
}
