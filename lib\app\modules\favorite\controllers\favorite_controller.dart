import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/model/score_query_prem.dart';
import 'package:news_lexica_app/app/data/repository/user_repository.dart';

import '../../../core/base/paging_controller.dart';
import '../../../data/model/score_board_data_response.dart';
import '../model/leader_ui_data.dart';
import '/app/core/base/base_controller.dart';

class FavoriteController extends BaseController {
  final UserRepository _repository = Get.find(tag: (UserRepository).toString());

  final RxList<LeaderUiData> _leaderListController = RxList.empty();

  List<LeaderUiData> get leaderList => _leaderListController.toList();

  final pagingController = PagingController<LeaderUiData>();

  void getScoreUserList() {
    if (!pagingController.canLoadNextPage()) return;

    pagingController.isLoadingPage = true;

    var queryParam = ScoreQueryPrem(pageNumber: pagingController.pageNumber);

    var scoreBoardService = _repository.getScoreBoard(queryParam);

    callDataService(
      scoreBoardService,
      onSuccess: _handleScoreListResponseSuccess,
    );

    pagingController.isLoadingPage = false;
  }

  void _handleScoreListResponseSuccess(ScoreBoardDataResponse response) {
    List<LeaderUiData>? repoList =
        response.scores
            ?.map(
              (e) => LeaderUiData(
                score: e.score != null ? e.score!.toString() : "Null",
                name: e.user != null ? e.user!.name! : "Null",
              ),
            )
            .toList();

    if (_isLastPage(repoList!.length, response.totalItems!)) {
      pagingController.appendLastPage(repoList);
    } else {
      pagingController.appendPage(repoList);
    }

    var newList = [...pagingController.listItems];

    _leaderListController(newList);
  }

  bool _isLastPage(int newListItemCount, int totalCount) {
    return (leaderList.length + newListItemCount) >= totalCount;
  }

  onRefreshPage() {
    pagingController.initRefresh();
    getScoreUserList();
  }

  onLoadNextPage() {
    logger.i("On load next");

    getScoreUserList();
  }
}
