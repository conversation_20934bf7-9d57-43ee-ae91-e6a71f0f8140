import 'package:dio/src/response.dart';
import 'package:news_lexica_app/app/core/model/update_user_pass_body_prem.dart';
import 'package:news_lexica_app/app/core/model/update_user_score_body_prem.dart';
import 'package:news_lexica_app/app/data/model/score_board_data_response.dart';
import 'package:news_lexica_app/app/data/model/user_data.dart';
import 'package:news_lexica_app/app/data/remote/user_remote_data_source.dart';

import '../../core/base/base_remote_source.dart';
import '../../core/model/score_query_prem.dart';
import '../../network/dio_provider.dart';

class UserRemoteDataSrcImpl extends BaseRemoteSource
    implements UserRemoteDataSrc {
  @override
  Future<UserData> getUserData(String userId) {
    var endpoint = "${DioProvider.baseUrl}/user/";
    var dioCall = dioClient.get(endpoint);

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => _parseUserResponse(response));
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<ScoreBoardDataResponse> getScoreBoard(ScoreQueryPrem scoreQueryPrem) {
    var endpoint = "${DioProvider.baseUrl}/user/getScoreList";
    var dioCall =
        dioClient.post(endpoint, queryParameters: scoreQueryPrem.toJson());

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => _parseSocreBoardUserResponse(response));
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> updateUserScore(
      UpdateUserScoreBodyPrem updateUserScoreBodyPrem) {
    var endpoint = "${DioProvider.baseUrl}/user/updateScoreById";
    var dioCall =
        dioClient.post(endpoint, data: updateUserScoreBodyPrem.toJson());

    try {
      return callApiWithErrorParser(dioCall).then((response) => response.data);
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> getUserScore(String userId) {
    var endpoint = "${DioProvider.baseUrl}/user/getUserScore";
    var dioCall = dioClient.post(endpoint, queryParameters: {"userId": userId});

    try {
      return callApiWithErrorParser(dioCall).then((response) => response.data);
    } catch (e) {
      rethrow;
    }

    throw UnimplementedError();
  }
}

_parseSocreBoardUserResponse(Response response) {
  return ScoreBoardDataResponse.fromJson(response.data);
}

_parseUserResponse(Response response) {
  return UserData.fromJson(response.data);
}
