import 'package:flutter/cupertino.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/values/app_values.dart';
import 'package:news_lexica_app/app/core/widget/custom_app_bar.dart';
import 'package:news_lexica_app/app/core/widget/loading.dart';
import 'package:news_lexica_app/app/modules/free_lesson/controller/free_lesson_controller.dart';

import '../widget/free_quiz_card.dart';

class FreeLessonView extends BaseView<FreeLessonController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return CustomAppBar(appBarTitleText: 'Stories & Newspaper');
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return Obx(() {
      if (controller.lessonList.isEmpty) {
        return Center(child: Loading());
      }

      return Padding(
        padding: const EdgeInsets.all(AppValues.halfPadding),
        child: AnimationLimiter(
          child: ListView.builder(
            //  shrinkWrap: true,
            //  physics: NeverScrollableScrollPhysics(),
            itemCount: controller.lessonList.length,
            itemBuilder: (context, index) {
              print("Building item $index"); // Debugging
              return AnimationConfiguration.staggeredList(
                position: index,
                duration: const Duration(milliseconds: 800),
                child: SlideAnimation(
                  verticalOffset: 50.0,
                  child: FadeInAnimation(
                    child: FreeQuizCard(
                      lesson: controller.lessonList[index],
                      index: index,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      );
    });

    throw UnimplementedError();
  }
}
