import 'dart:math';

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/values/app_values.dart';
import 'package:news_lexica_app/app/modules/quiz_two/controller/quiz_two_controller.dart';

import '../../../core/values/text_styles.dart';

class QuizTwoView extends BaseView<QuizTwoController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return PopScope(
      onPopInvoked: (bool didPop) {
        if (didPop) {
          return;
        }
        closeDialog();
      },
      canPop: false,
      child: Safe<PERSON>rea(
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                    onPressed: () {
                      closeDialog();
                    },
                    icon: Icon(
                      Icons.close,
                      size: 29,
                    )),
                Obx(
                  () => Center(
                    child: Text(
                      "Score ${controller.list.where((p0) => p0==Answer.right,).length}/${controller.quiz2.length}",
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                  ),
                ),
                Text('')
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppValues.padding),
              child: Text(
                  "নিচের বাংলা শব্দের story তে প্রদত্ত English word বক্সে লিখুন। English word টির 1st letter টি দিয়ে দেয়া হলো। পাশের সাবমিট বাটনে টেপ করে দেখে নিন আপনার উত্তর সঠিক কিনা।"),
            ),
            Obx(
              () => Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: AppValues.padding),
                  child: ListView.builder(
                    itemCount:
                        controller.endIndex.value - controller.startIndex.value,
                    itemBuilder: (context, index) {
                      return Obx(() => Card(
                            color: controller.list[controller.startIndex.value + index] ==
                                    Answer.right
                                ? Colors.green[200]
                                : controller.list[
                                            controller.startIndex.value + index] ==
                                        Answer.wrong
                                    ? Colors.red[200]
                                    : Theme.of(context).cardColor,
                            child: ListTile(
                                title: Text(
                                  controller
                                      .quiz2[controller.startIndex.value + index]
                                      .question,
                                ),
                                subtitle: TextField(
                                  controller: controller.textControllers[
                                      controller.startIndex.value + index],
                                  decoration: const InputDecoration(
                                      filled: true,
                                      hintText: "Enter correct translation",
                                      border: OutlineInputBorder()),
                                ),
                                trailing: IconButton(
                                  onPressed: () async {


                                    int randomNumber = controller.random.nextInt(12)+1;
                                    if (controller.list[
                                            controller.startIndex.value + index] ==
                                        Answer.pending) {
                                      String enteredText = controller
                                          .textControllers[
                                              controller.startIndex.value + index]
                                          .text
                                          .trim();
                                      // ?quizInputController.updateScoreQuiz1:null
                                      if (controller.quiz2CheckAnswer(
                                          enteredText,
                                          controller.quiz2[
                                              controller.startIndex.value +
                                                  index])) {
                                        await controller.player.play(AssetSource('audio/correct${randomNumber}.mp3'));
                                      //  controller.updateScoreQuiz2();
                                        controller.list[
                                            controller.startIndex.value +
                                                index] = Answer.right;
                                      } else {
                                        await controller.player.play(AssetSource('audio/wrong.mp3'));
                                        controller
                                                .textControllers[controller
                                                        .startIndex.value +
                                                    index]
                                                .text =
                                            "Correct Answer: ${controller.quiz2[controller.startIndex.value + index].answer}";
                                        controller.list[
                                            controller.startIndex.value +
                                                index] = Answer.wrong;
                                      }
                                    } else {}
                                  },
                                  icon: controller.list[
                                              controller.startIndex.value +
                                                  index] ==
                                          Answer.pending
                                      ? Icon((Icons.send))
                                      : controller.list[
                                                  controller.startIndex.value +
                                                      index] ==
                                              Answer.right
                                          ? Icon((Icons.done))
                                          : Icon((Icons.close)),
                                )),
                          ));
                    },
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      //
                      //
                      if (controller.startIndex.value == 0) {
                        Get.snackbar("News Lexcia", "No previous page");
                      } else {
                        controller.previousQuestion();
                      }

                      print(controller.startIndex.value);
                      print(controller.quiz2.length);
                    },
                    child: Text("Previous Page",
                        style: TextStyle(color: Colors.teal)),
                    style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                            side: BorderSide(color: Colors.teal)),
                        minimumSize: Size(Get.width * .45, 50)),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      controller.nextQuestion();

                      if (controller.startIndex.value >=
                          controller.quiz2.length) {
                        _showDialog(controller.list.where((p0) => p0==Answer.right,).length);
                      }
                    },
                    child:
                        Text("Next Page", style: TextStyle(color: Colors.white)),
                    style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8)),
                        backgroundColor: Colors.teal,
                        minimumSize: Size(Get.width * .45, 50)),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
    throw UnimplementedError();
  }

  void _showDialog(int score) {
    Get.dialog(
      AlertDialog(
        title:  Text('Your Score: $score/${controller.list.length}'),
        content: SizedBox(
          height: Get.height*.3,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "Congratulations 👏",
                style: titleStyle,
              ),
              Text(
                "You completed the quiz.",
                style: subTitleStyle,
              ),
              SizedBox(
                height: AppValues.padding,
              ),
              Text(
                controller.list.where((p0) => p0 == Answer.wrong).length > 1
                    ? "Take the quiz again to correct your mistake."
                    : "",
                style: subTitleStyle,
              ),
            ],
          ),
        ),
        actions: [


          TextButton(
            onPressed: () {
              controller.updateScore("$score");
            },
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  void closeDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Quiz'),
        content: const Text('Are you sure you want to quit the quiz?'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text('No'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.back();
            },
            child: Text('yes'),
          ),
        ],
      ),
    );
  }
}
