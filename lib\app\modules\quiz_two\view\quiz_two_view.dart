import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/modules/quiz_two/controller/quiz_two_controller.dart';
import '../widget/quiz_completion_dialog.dart';
import '../widget/quiz_exit_dialog.dart';
import '../widget/quiz_header.dart';
import '../widget/quiz_instructions.dart';
import '../widget/quiz_navigation_buttons.dart';
import '../widget/quiz_questions_list.dart';

class QuizTwoView extends BaseView<QuizTwoController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) => null;

  @override
  Widget body(BuildContext context) {
    return PopScope(
      onPopInvoked: (bool didPop) {
        if (didPop) return;
        _showExitDialog();
      },
      canPop: false,
      child: <PERSON><PERSON><PERSON>(
        child: Column(
          children: [
            const QuizHeader(),
            const QuizInstructions(),
            Expanded(
              child: QuizQuestionsList(
                controller: controller,
                onAnswerSubmitted: _handleAnswerSubmission,
              ),
            ),
            QuizNavigationButtons(
              controller: controller,
              onPrevious: _handlePreviousPage,
              onNext: _handleNextPage,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleAnswerSubmission(int questionIndex) async {
    final globalIndex = controller.startIndex.value + questionIndex;

    if (controller.list[globalIndex] != Answer.pending) return;

    final enteredText = controller.textControllers[globalIndex].text.trim();
    final isCorrect = controller.quiz2CheckAnswer(
      enteredText,
      controller.quiz2[globalIndex],
    );

    if (isCorrect) {
      await _playCorrectSound();
      controller.list[globalIndex] = Answer.right;
    } else {
      await _playWrongSound();
      controller.textControllers[globalIndex].text =
          "Correct Answer: ${controller.quiz2[globalIndex].answer}";
      controller.list[globalIndex] = Answer.wrong;
    }
  }

  void _handlePreviousPage() {
    if (controller.startIndex.value == 0) {
      Get.snackbar("News Lexcia", "No previous page");
    } else {
      controller.previousQuestion();
    }
  }

  void _handleNextPage() {
    controller.nextQuestion();
    if (controller.startIndex.value >= controller.quiz2.length) {
      final score =
          controller.list.where((answer) => answer == Answer.right).length;
      _showCompletionDialog(score);
    }
  }

  Future<void> _playCorrectSound() async {
    final randomNumber = controller.random.nextInt(12) + 1;
    await controller.player.play(AssetSource('audio/correct$randomNumber.mp3'));
  }

  Future<void> _playWrongSound() async {
    await controller.player.play(AssetSource('audio/wrong.mp3'));
  }

  void _showCompletionDialog(int score) {
    Get.dialog(
      QuizCompletionDialog(
        score: score,
        totalQuestions: controller.list.length,
        hasWrongAnswers:
            controller.list.where((answer) => answer == Answer.wrong).length >
            1,
        onOkPressed: () => controller.updateScore("$score"),
      ),
    );
  }

  void _showExitDialog() {
    Get.dialog(
      QuizExitDialog(
        onConfirmExit: () {
          Get.back(); // Close dialog
          Get.back(); // Close quiz screen
        },
      ),
    );
  }
}
