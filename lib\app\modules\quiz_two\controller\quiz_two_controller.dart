import 'dart:math';

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import 'package:news_lexica_app/app/modules/lesson_details/model/quiz_two_ui_data.dart';

import '../../../core/model/update_user_score_body_prem.dart';
import '../../../data/repository/user_repository.dart';

enum Answer { right, wrong, pending }

class QuizTwoController extends BaseController {
  final UserRepository _repositoryUser =
      Get.find(tag: (UserRepository).toString());

  final player = AudioPlayer();

  List<TextEditingController> textControllers = [];

  Random random = Random();
  var questionQuiz2 = 0.obs;

  var quiz2 = <QuizTwoUiData>[].obs;

  var startIndex = 0.obs;
  var endIndex = 5.obs;

  var list = <Answer>[].obs;

  clearQuiz() {
    questionQuiz2.value = 0;
  }

  updateQuestionQuiz2() => questionQuiz2.value++;

  String getQuizImage(int score, int quizSize) {
    var percentage = (score / quizSize) * 100;
    if (percentage > 80) {
      return "assets/gold.png";
    } else if (percentage > 60) {
      return "assets/silver.png";
    }
    if (percentage > 30) {
      return "assets/bronze.png";
    } else {
      return "assets/failed.png";
    }
  }

  String getQuizText(int score, int quizSize) {
    var percentage = (score / quizSize) * 100;
    if (percentage > 80) {
      return "Brilliant Work!";
    } else if (percentage > 60) {
      return "Excellent Work!";
    }
    if (percentage > 30) {
      return "Good Work";
    } else {
      return "Try again latter";
    }
  }

  updateScore(String score) {

    var updateScoreService = _repositoryUser.updateUserScore(
        UpdateUserScoreBodyPrem(userId: "", score: score));
    callDataService(updateScoreService,
        onSuccess: _handleScoreUpdateSuccess, onError: _handleScoreUpdateError);
  }

  _handleScoreUpdateSuccess(Map<String, dynamic> response) {
    Get.back();
    Get.back();
  }

  _handleScoreUpdateError(Exception exception) {
    Fluttertoast.showToast(msg: errorMessage);
    Get.back();
    Get.back();
  }

  nextQuestion() {
    startIndex.value = endIndex.value;
    endIndex.value = (endIndex.value + 5).clamp(0, quiz2.length);
  }

  previousQuestion() {
    endIndex.value = startIndex.value;
    startIndex.value = (startIndex.value - 5).clamp(0, quiz2.length);
  }

  bool quizAvailable() {
    if (questionQuiz2.value + 1 >= quiz2.length) {
      return false;
    } else {
      return true;
    }
  }

  bool quiz2CheckAnswer(String answer, QuizTwoUiData question) {
    if (question.answer.toString().toLowerCase().trim() ==
        answer.toLowerCase().trim()) {
      return true;
    } else {
      return false;
    }
  }
  //
  // void initializeQuizzes() {
  //   quiz2.shuffle();
  // }

  @override
  void onInit() {
    var dataModel = Get.arguments;
    if (dataModel is List<QuizTwoUiData>) {
      quiz2(dataModel);
      textControllers = List.generate(
        dataModel.length,
        (index) => TextEditingController(text: dataModel[index].answer[0]),
      );
      list.value = List.generate(dataModel.length, (index) => Answer.pending);
    }
    super.onInit();
  }
}
