import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/values/app_values.dart';
import 'package:news_lexica_app/app/core/widget/elevated_container.dart';

import '../../../../routes/app_pages.dart';
import '../model/book_data_model.dart';

class BookListCard extends StatelessWidget {
  const BookListCard({Key? key, required this.book}) : super(key: key);

  final BookDataModel book;

  @override
  Widget build(BuildContext context) {
    return ElevatedContainer(
      bgColor: Theme.of(context).cardColor,
      child: Padding(
        padding: const EdgeInsets.all(AppValues.padding),
        child: ListTile(
          leading: Image.asset("assets/ielts_inside.png"),
          onTap: () {
            Get.toNamed(Routes.NEW_IELTS_READING_LIST, arguments: book.id);
          },
          title: Text(
            book.title,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          trailing: IconButton(
            icon: Icon(Icons.chevron_right),
            onPressed: () {
              Get.toNamed(Routes.NEW_IELTS_READING_LIST, arguments: book.id);
            },
          ),
        ),
      ),
    );
  }
}
