class UpdateUserScoreBodyPrem {
  String userId;
  String score;

  // Constructor
  UpdateUserScoreBodyPrem({
    required this.userId,
    required this.score,
  });

  // Method to convert instance to JSON map
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'score':score,
    };
  }

  // Optional: Method to create an instance from JSON map
  factory UpdateUserScoreBodyPrem.fromJson(Map<String, dynamic> json) {
    return UpdateUserScoreBodyPrem(
      userId: json['userId'],
      score: json['score'],
    );
  }
}
