import 'package:flutter/cupertino.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/widget/custom_app_bar.dart';

import '../controller/quiz_mistake_controller.dart';

class QuizMistakeView extends BaseView<QuizMistakeController>{
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return CustomAppBar(appBarTitleText: "Revise Section");
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return Center(child: Text("Nothing to revise"));
    throw UnimplementedError();
  }

}