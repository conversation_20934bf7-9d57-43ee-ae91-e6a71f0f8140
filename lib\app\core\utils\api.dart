import 'package:url_launcher/url_launcher.dart';

var baseURL = 'http://*************:5000';

class Api {
  // FirebaseAuth auth = FirebaseAuth.instance;
  void launchWhatsApp(
      {required String phoneNumber, required String message}) async {
    final Uri whatsappUri = Uri(
      scheme: 'https',
      host: 'api.whatsapp.com',
      path: 'send',
      queryParameters: {
        'phone': phoneNumber,
        'text': message,
      },
    );

    final url = whatsappUri.toString();
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  void launchURL(String url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }
}
