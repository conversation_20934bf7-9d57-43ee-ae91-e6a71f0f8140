import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/values/app_values.dart';
import 'package:news_lexica_app/app/modules/lesson_discussion/controller/quiz_discussion_controller.dart';
import '../../../core/values/app_colors.dart';
import '../model/comment_ui_data.dart';

class CommentCard extends StatefulWidget {
  final CommentUiData model;
  final bool isMe;
  const CommentCard({Key? key, required this.model, required this.isMe})
    : super(key: key);

  @override
  _CommentCardState createState() => _CommentCardState();
}

class _CommentCardState extends State<CommentCard> {
  bool showReplies = false;
  QuizDiscussionController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
        left: widget.model.parentId != "null" ? 20.0 : 0,
        top: 8,
        bottom: 8,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppValues.radius_6),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: Colors.grey[200],
                  child: ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: "http://via.placeholder.com/150",
                      fit: BoxFit.cover,
                      progressIndicatorBuilder:
                          (context, url, progress) => SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              value: progress.progress,
                              strokeWidth: 2,
                            ),
                          ),
                      errorWidget:
                          (context, url, error) =>
                              Icon(Icons.person, size: 18, color: Colors.grey),
                    ),
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Wrap(
                    crossAxisAlignment: WrapCrossAlignment.center,
                    children: [
                      if (widget.model.userId == '2')
                        Padding(
                          padding: const EdgeInsets.only(right: 4),
                          child: Icon(
                            Icons.edit,
                            size: 14,
                            color: Colors.orange,
                          ),
                        ),
                      Text(
                        widget.model.userName,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 13,
                          color: Colors.black87,
                        ),
                      ),
                      SizedBox(width: 8),
                      Text(
                        widget.model.time,
                        style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            SizedBox(height: 6),

            // Comment text
            Padding(
              padding: const EdgeInsets.only(left: 4),
              child: Text(
                widget.model.comment,
                style: TextStyle(fontSize: 14, height: 1.4),
              ),
            ),

            SizedBox(height: 8),

            // Action row
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
                  onPressed: () {
                    controller.commentController.text =
                        "@${widget.model.userName}";
                    controller.replyId = "${widget.model.id}";
                  },
                  child: Text("Reply", style: TextStyle(fontSize: 13)),
                ),
              ],
            ),

            // Replies
            if (widget.model.reply.isNotEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  GestureDetector(
                    onTap: () => setState(() => showReplies = !showReplies),
                    child: Padding(
                      padding: const EdgeInsets.only(left: 4, bottom: 4),
                      child: Text(
                        showReplies
                            ? "Hide Replies"
                            : "View Replies (${widget.model.reply.length})",
                        style: TextStyle(
                          color: Colors.blue,
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  AnimatedCrossFade(
                    firstChild: Container(),
                    secondChild: Padding(
                      padding: const EdgeInsets.only(left: 12.0),
                      child: Column(
                        children:
                            widget.model.reply
                                .map(
                                  (replyData) => CommentCard(
                                    model: replyData,
                                    isMe: false,
                                  ),
                                )
                                .toList(),
                      ),
                    ),
                    crossFadeState:
                        showReplies
                            ? CrossFadeState.showSecond
                            : CrossFadeState.showFirst,
                    duration: Duration(milliseconds: 300),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
