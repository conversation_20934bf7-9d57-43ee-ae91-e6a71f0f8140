import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/values/app_values.dart';
import 'package:news_lexica_app/app/core/widget/elevated_container.dart';
import 'package:news_lexica_app/app/modules/lesson_discussion/controller/quiz_discussion_controller.dart';

import '../../../core/values/app_colors.dart';
import '../model/comment_ui_data.dart';

class CommentCard extends StatefulWidget {
  final CommentUiData model;
  final bool isMe;
  const CommentCard({Key? key, required this.model, required this.isMe})
    : super(key: key);

  @override
  _CommentCardState createState() => _CommentCardState();
}

class _CommentCardState extends State<CommentCard> {
  bool showReplies = false;
  QuizDiscussionController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: widget.model.parentId != "null" ? 16.0 : 0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppValues.radius_6),
        boxShadow: [
          BoxShadow(
            color: AppColors.elevatedContainerColorOpacity,
            spreadRadius: 1,
            blurRadius: 1,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row with avatar and user information
            Row(
              children: [
                CircleAvatar(
                  radius: 12,
                  child: CachedNetworkImage(
                    imageUrl: "http://via.placeholder.com/350x150",
                    progressIndicatorBuilder:
                        (context, url, downloadProgress) =>
                            CircularProgressIndicator(
                              value: downloadProgress.progress,
                            ),
                    errorWidget: (context, url, error) => Icon(Icons.person),
                  ),
                ),
                SizedBox(width: AppValues.halfPadding),
                Wrap(
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    widget.model.userId == '2'
                        ? Icon(Icons.edit, size: 12)
                        : Container(),
                    Text(
                      widget.model.userId == '2' ? " Author" : "",
                      style: TextStyle(fontSize: 12),
                    ),
                  ],
                ),
                SizedBox(width: 8),
                Text(
                  widget.model.userName,
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 11),
                ),
                SizedBox(width: 8),
                Text(
                  widget.model.time,
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 11),
                ),
              ],
            ),
            SizedBox(height: 8),
            // Comment text
            SizedBox(
              width: Get.width * .7,
              child: Padding(
                padding: const EdgeInsets.only(left: AppValues.padding_4),
                child: Text(
                  widget.model.comment,
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
            SizedBox(height: AppValues.padding),
            // Vote and reply buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    controller.commentController.text =
                        "@${widget.model.userName}";
                    controller.replyId = "${widget.model.id}";
                  },
                  child: Text("Reply"),
                ),
              ],
            ),
            // View/hide replies button and nested replies list
            if (widget.model.reply.isNotEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextButton(
                    onPressed: () {
                      setState(() {
                        showReplies = !showReplies;
                      });
                    },
                    child: Text(
                      showReplies
                          ? "Hide Replies"
                          : "View Replies (${widget.model.reply.length})",
                      style: TextStyle(color: Colors.blue),
                    ),
                  ),
                  if (showReplies)
                    Padding(
                      padding: const EdgeInsets.only(left: 16.0),
                      child: Column(
                        children:
                            widget.model.reply.map((replyData) {
                              // For nested replies, we pass false or calculate isMe as needed.
                              return CommentCard(model: replyData, isMe: false);
                            }).toList(),
                      ),
                    ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
