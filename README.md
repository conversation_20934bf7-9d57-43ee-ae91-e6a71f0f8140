# News Lexica App

A Flutter application for learning English through news articles with multiple environment configurations.

## Project Overview

News Lexica is an educational app that helps users improve their English language skills by reading news articles, taking quizzes, and learning vocabulary. The app supports multiple flavors for different deployment environments.

## App Flavors

This project is configured with two flavors to support different environments:

### Development (dev)
- **Purpose**: For development and testing
- **App Name**: News Lexica Dev
- **Package ID**: `com.munir.newslexica.news_lexica_app.dev` (Android)
- **Bundle ID**: `com.munir.newslexica.news_lexica_app.dev` (iOS)
- **API Endpoint**: `https://server.dreamersenglish.com/api/v2`
- **Features**: Debug logging enabled, crash reporting enabled

### Production (prod)
- **Purpose**: For production releases
- **App Name**: News Lexica
- **Package ID**: `com.munir.newslexica.news_lexica_app` (Android)
- **Bundle ID**: `com.munir.newslexica.news_lexica_app` (iOS)
- **API Endpoint**: `https://server.dreamersenglish.com/api/v2`
- **Features**: Optimized performance, crash reporting enabled, notifications enabled

## Getting Started

### Prerequisites
- Flutter SDK (3.7.2 or higher)
- Dart SDK
- Android Studio / Xcode for platform-specific development
- VS Code (recommended) with Flutter extension

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd news_lexica_app
```

2. Install dependencies:
```bash
flutter pub get
```

3. Generate localization files:
```bash
flutter gen-l10n
```

## Running the App

### Using Flutter Commands

#### Development Flavor
```bash
# Debug mode
flutter run --flavor dev -t lib/main_dev.dart

# Profile mode
flutter run --flavor dev -t lib/main_dev.dart --profile

# Release mode
flutter run --flavor dev -t lib/main_dev.dart --release
```

#### Production Flavor
```bash
# Debug mode
flutter run --flavor prod -t lib/main_prod.dart

# Profile mode
flutter run --flavor prod -t lib/main_prod.dart --profile

# Release mode
flutter run --flavor prod -t lib/main_prod.dart --release
```

### Using VS Code

The project includes pre-configured launch configurations in `.vscode/launch.json`:

1. **Development** - Run dev flavor in debug mode
2. **Production** - Run prod flavor in debug mode
3. **Development (Profile)** - Run dev flavor in profile mode
4. **Production (Profile)** - Run prod flavor in profile mode
5. **Development (Release)** - Run dev flavor in release mode
6. **Production (Release)** - Run prod flavor in release mode

To use these configurations:
1. Open the project in VS Code
2. Go to Run and Debug (Ctrl+Shift+D)
3. Select the desired configuration from the dropdown
4. Press F5 or click the play button

## Building for Release

### Android

#### Development APK
```bash
flutter build apk --flavor dev -t lib/main_dev.dart
```

#### Production APK
```bash
flutter build apk --flavor prod -t lib/main_prod.dart
```

#### Android App Bundle (for Play Store)
```bash
# Development
flutter build appbundle --flavor dev -t lib/main_dev.dart

# Production
flutter build appbundle --flavor prod -t lib/main_prod.dart
```

### iOS

#### Development
```bash
flutter build ios --flavor dev -t lib/main_dev.dart
```

#### Production
```bash
flutter build ios --flavor prod -t lib/main_prod.dart
```

## Project Structure

```
lib/
├── app/                    # Main application code
│   ├── bindings/          # Dependency injection bindings
│   ├── core/              # Core utilities and base classes
│   ├── data/              # Data layer (repositories, models)
│   ├── modules/           # Feature modules
│   ├── network/           # Network layer
│   ├── routes/            # App routing
│   └── my_app.dart        # Main app widget
├── flavors/               # Flavor configuration
│   ├── build_config.dart  # Build configuration singleton
│   ├── env_config.dart    # Environment-specific config
│   └── environment.dart   # Environment enum
├── l10n/                  # Localization files
├── main_dev.dart          # Development entry point
├── main_prod.dart         # Production entry point
└── theme.dart             # App theming
```

## Features

- 📰 **News Reading**: Browse and read news articles in English
- 🎯 **Interactive Quizzes**: Test comprehension with various quiz types
- 📚 **Vocabulary Learning**: Learn new words with definitions and examples
- 🎵 **Audio Support**: Listen to pronunciations and audio content
- 🌍 **Localization**: Support for multiple languages (English, Bengali)
- 🎨 **Theming**: Light and dark theme support
- 📱 **Cross-platform**: Runs on Android and iOS
- 🔔 **Notifications**: Push notifications for new content (Production only)

## Dependencies

Key dependencies used in this project:

- **State Management**: GetX
- **Networking**: Dio with caching
- **UI Components**: Material Design, Google Fonts
- **Audio**: AudioPlayers
- **Localization**: Flutter Intl
- **Notifications**: Flutter Local Notifications
- **Web Content**: WebView Flutter, Flutter HTML

## Development Guidelines

### Adding New Features
1. Create feature modules in `lib/app/modules/`
2. Follow GetX pattern (Controller, View, Binding)
3. Add routes in `lib/app/routes/`
4. Update localization files if needed

### Environment Configuration
- Modify `lib/flavors/env_config.dart` for environment-specific settings
- Update main entry points (`main_dev.dart`, `main_prod.dart`) for flavor-specific initialization
- Add new environments in `lib/flavors/environment.dart` if needed

### Testing
```bash
# Run all tests
flutter test

# Run tests with coverage
flutter test --coverage
```

## Troubleshooting

### Common Issues

1. **Build failures**: Ensure all dependencies are installed with `flutter pub get`
2. **Flavor not recognized**: Make sure you're using the correct flavor name (`dev` or `prod`)
3. **iOS build issues**: Run `cd ios && pod install` to update CocoaPods dependencies
4. **Localization issues**: Run `flutter gen-l10n` to regenerate localization files

### Getting Help

- Check the [Flutter documentation](https://docs.flutter.dev/)
- Review [GetX documentation](https://github.com/jonataslaw/getx) for state management
- For project-specific issues, check the issue tracker

## License

This project is proprietary software. All rights reserved.
