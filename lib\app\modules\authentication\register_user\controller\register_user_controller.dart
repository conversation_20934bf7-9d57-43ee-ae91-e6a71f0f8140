import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import 'package:news_lexica_app/app/core/model/signup_body_prem.dart';
import 'package:news_lexica_app/app/routes/app_pages.dart';

import '../../../../core/model/sign_up_data.dart';
import '../../../../data/repository/authentication_repository.dart';

class RegisterUserController extends BaseController {
  var isPasswordObscured = false.obs;

  void togglePasswordVisibility() {
    isPasswordObscured.value = !isPasswordObscured.value;
  }

  final AuthenticationRepository _repository = Get.find(
    tag: (AuthenticationRepository).toString(),
  );

  var nameController = TextEditingController();
  var phoneController = TextEditingController();
  var passwordController = TextEditingController();

  registerUpUser() {
    var signUpUserService = _repository.registerUser(
      SignUpBodyPrem(
        nameController.text!,
        phoneController.text!,
        passwordController.text!,
      ),
    );
    callDataService(
      signUpUserService,
      onSuccess: _handleSignUpSuccess,
      onError: _handleSignUpError,
    );
  }

  _handleSignUpSuccess(Map<String, dynamic> response) {
    Fluttertoast.showToast(msg: "Registration Complete");
    Get.offAllNamed(Routes.USER_LOGIN);
  }

  _handleSignUpError(Exception exception) {
    Fluttertoast.showToast(msg: errorMessage);
  }
}
