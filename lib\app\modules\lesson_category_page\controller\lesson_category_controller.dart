import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import 'package:news_lexica_app/app/core/model/lesson_search_query_parm.dart';
import 'package:news_lexica_app/app/data/repository/user_repository.dart';
import '../../../core/model/main_category.dart';
import '../../../data/model/lesson_data.dart';
import '../../../data/repository/lesson_repository.dart';
import '../../home/<USER>/lesson_ui_data.dart';
import '../../main/controllers/main_controller.dart';

class LessonCategoryController extends BaseController {
  var isSubscribed = false.obs;

  MainController mainController = Get.find();

  final LessonRepository _repository = Get.find(
    tag: (LessonRepository).toString(),
  );

  final UserRepository _repositoryUser = Get.find(
    tag: (UserRepository).toString(),
  );

  final RxList<LessonUiData> _lessonListController = RxList.empty();

  List<LessonUiData> get lessonList => _lessonListController.toList();

  MainCategory mainCategory = MainCategory(title: '', subtitle: '', assets: '');

  void getLessonList() {
    var queryParam = LessonSearchQueryParam(
      contentType: 1,
      part: 1,
      category: mainCategory.title,
    );

    var lessonSearchService = _repository.getLessonList(queryParam);

    callDataService(
      lessonSearchService,
      onSuccess: _handleLessonListResponseSuccess,
    );
  }

  void _handleLessonListResponseSuccess(LessonData response) {
    List<LessonUiData>? repoList =
        response.message
            ?.map(
              (e) => LessonUiData(
                pressName: e.pressName != null ? e.pressName! : "Null",
                title: e.title != null ? e.title! : "Null",
                lessonNumber: e.lessonNumber != null ? e.lessonNumber! : 0,
                category: e.category != null ? e.category! : "Null",
                id: e.id != null ? e.id! : 0,
              ),
            )
            .toList();

    repoList?.sort((a, b) => a.lessonNumber!.compareTo(b.lessonNumber as num));
    _lessonListController(repoList);
  }

  @override
  void onInit() {
    var dataModel = Get.arguments;
    if (dataModel is MainCategory) {
      isSubscribed = mainController.isSubscribed;
      mainCategory = dataModel;
      getLessonList();
    }
    super.onInit();
  }

  _handleError(Exception exception) {}
}
