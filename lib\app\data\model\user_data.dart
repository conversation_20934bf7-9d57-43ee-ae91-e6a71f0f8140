class UserData {
  Message? message;

  UserData({this.message});

  UserData.fromJson(Map<String, dynamic> json) {
    message =
        json['message'] != null ? new Message.fromJson(json['message']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.message != null) {
      data['message'] = this.message!.toJson();
    }
    return data;
  }
}

class Message {
  int? id;
  String? name;
  Null? photo;
  String? password;
  String? phone;
  String? refreshToken;
  SubscriptionRequest? subscriptionRequest;
  ScoreBoard? scoreBoard;

  Message(
      {this.id,
      this.name,
      this.photo,
      this.password,
      this.phone,
      this.refreshToken,
      this.subscriptionRequest,
      this.scoreBoard});

  Message.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    photo = json['photo'];
    password = json['password'];
    phone = json['phone'];
    refreshToken = json['refreshToken'];
    subscriptionRequest = json['SubscriptionRequest'] != null
        ? new SubscriptionRequest.fromJson(json['SubscriptionRequest'])
        : null;
    scoreBoard = json['ScoreBoard'] != null
        ? new ScoreBoard.fromJson(json['ScoreBoard'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['photo'] = this.photo;
    data['password'] = this.password;
    data['phone'] = this.phone;
    data['refreshToken'] = this.refreshToken;
    if (this.subscriptionRequest != null) {
      data['SubscriptionRequest'] = this.subscriptionRequest!.toJson();
    }
    if (this.scoreBoard != null) {
      data['ScoreBoard'] = this.scoreBoard!.toJson();
    }
    return data;
  }
}

class SubscriptionRequest {
  bool? subscribed;
  String? createdAt;
  String? duration;
  String? status;

  SubscriptionRequest(
      {this.subscribed, this.createdAt, this.duration, this.status});

  SubscriptionRequest.fromJson(Map<String, dynamic> json) {
    subscribed = json['subscribed'];
    createdAt = json['createdAt'];
    duration = json['duration'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['subscribed'] = this.subscribed;
    data['createdAt'] = this.createdAt;
    data['duration'] = this.duration;
    data['status'] = this.status;
    return data;
  }
}

class ScoreBoard {
  int? score;

  ScoreBoard({this.score});

  ScoreBoard.fromJson(Map<String, dynamic> json) {
    score = json['score'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['score'] = this.score;
    return data;
  }
}
