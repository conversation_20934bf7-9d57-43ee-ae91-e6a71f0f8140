import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/routes/app_pages.dart';
import '../../../core/values/data.dart';
import '../../home/<USER>/lesson_ui_data.dart';

class FreeQuizCard extends StatelessWidget {
  const FreeQuizCard({super.key, required this.lesson, required this.index});
  final LessonUiData lesson;
  final int index;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Get.toNamed(Routes.LESSON_DETAILS, arguments: lesson.lessonNumber);
      },
      child: Container(
        width: double.infinity,
        child: Stack(
          children: [
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: lightColors[index],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
                borderRadius: BorderRadius.circular(15.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 8.0,
                    offset: Offset(0.0, 5.0),
                  ),
                ],
              ),
              margin: EdgeInsets.all(5),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "${lesson.category}",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Color(0xff8fc7ff),
                        fontSize: 18,
                      ),
                    ),
                    const SizedBox(height: 5),
                    Text(
                      'Story ${index + 1}',
                      style: TextStyle(color: Colors.white),
                    ),
                    const SizedBox(height: 5),
                    RichText(
                      text: TextSpan(
                        text: '${lesson.title}',
                        style: TextStyle(color: Colors.white, fontSize: 16),
                        children: <TextSpan>[
                          TextSpan(
                            text: '${lesson.pressName}',
                            style: TextStyle(
                              fontStyle: FontStyle.italic,
                              color: Colors.blue,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 5),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
