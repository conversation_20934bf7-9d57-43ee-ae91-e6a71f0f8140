import 'dart:math';
import 'package:audioplayers/audioplayers.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import 'package:news_lexica_app/app/core/model/update_user_score_body_prem.dart';
import '../../../core/model/quiz.dart';
import '../../../data/repository/user_repository.dart';
import '../../lesson_details/model/quiz_one_ui.dart';
import 'package:in_app_review/in_app_review.dart';

enum Answer { right, wrong, pending }

class QuizOneController extends BaseController {
  final UserRepository _repositoryUser =
      Get.find(tag: (UserRepository).toString());

  final InAppReview inAppReview = InAppReview.instance;

  String getQuizImage(int score, int quizSize) {
    var percentage = (score / quizSize) * 100;
    if (percentage > 80) {
      return "assets/gold.png";
    } else if (percentage > 60) {
      return "assets/silver.png";
    }
    if (percentage > 30) {
      return "assets/bronze.png";
    } else {
      return "assets/failed.png";
    }
  }

  String getQuizText(int score, int quizSize) {
    var percentage = (score / quizSize) * 100;
    if (percentage > 80) {
      return "Brilliant Work!";
    } else if (percentage > 60) {
      return "Excellent Work!";
    }
    if (percentage > 30) {
      return "Good Work";
    } else {
      return "Try again latter";
    }
  }

  Random random = Random();

  void previousQuestion() {
    if (currentIndex > 0) {
      backQuestion();
    }
  }

  void getNextQuestion() {
    if (currentIndex.value < quiz3.length - 1) {
      nextQuestion();
    }
  }

  final player = AudioPlayer();

  var list = <Answer>[].obs;

  var currentIndex = 0.obs;
  var quiz3 = <Quiz>[].obs;

  clearQuiz() {
    currentIndex.value = 0;
  }

  resetQuiz() {
    currentIndex.value = 0;
  }

  nextQuestion() => currentIndex++;
  backQuestion() => currentIndex--;

  void initializeQuizzes() {
    quiz3.shuffle();
  }

  bool quizAvailable() {
    if (currentIndex.value + 1 >= quiz3.length) {
      return false;
    } else {
      return true;
    }
  }

  updateScore(String score) {
    var updateScoreService = _repositoryUser
        .updateUserScore(UpdateUserScoreBodyPrem(userId: "", score: score));
    callDataService(updateScoreService,
        onSuccess: _handleScoreUpdateSuccess, onError: _handleScoreUpdateError);
  }

  bool quizCheckAnswer(String answer, Quiz quiz) {
    return quiz.answer.toString().toLowerCase().trim() ==
            answer.toLowerCase().trim()
        ? true
        : false;
  }

  getLesson(List<QuizOneUiData> lesson) {
    lesson.forEach((element) {
      quiz3.add(Quiz(choices: [
        element.option1,
        element.option2,
        element.option3,
        element.option4
      ], question: element.question, answer: element.answer));
    });
  }

  @override
  Future<void> onInit() async {
    var dataModel = Get.arguments;
    if (dataModel is List<QuizOneUiData>) {
      getLesson(dataModel);
      list.value = List.generate(dataModel.length, (index) => Answer.pending);
    }
    super.onInit();
  }

  _handleScoreUpdateSuccess(Map<String, dynamic> response) {
    Get.back();
    Get.back();
  }

  _handleScoreUpdateError(Exception exception) {
    Fluttertoast.showToast(msg: errorMessage);
    Get.back();
    Get.back();
  }
}
