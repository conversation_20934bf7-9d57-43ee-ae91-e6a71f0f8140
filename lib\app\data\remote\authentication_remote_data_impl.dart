import 'package:news_lexica_app/app/core/model/update_user_pass_body_prem.dart';
import 'package:news_lexica_app/app/data/remote/authentication_remote_data_source.dart';

import '../../core/base/base_remote_source.dart';
import '../../core/model/signup_body_prem.dart';
import '../../network/dio_provider.dart';

class AuthenticationRemoteDataImpl extends BaseRemoteSource
    implements AuthenticationRemoteDataSource{



  @override
  Future<Map<String, dynamic>> checkUserExist(String phone) {
    var endpoint = "${DioProvider.baseUrl}/register/userExist";
    var dioCall = dioClient.get(endpoint,queryParameters: {
      "phone":phone
    } );

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => response.data);
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> matchOtp(String enteredOtp,String secretOtp) {
    var endpoint = "${DioProvider.baseUrl}/auth/verifyOtp";
    var dioCall = dioClient.post(endpoint, queryParameters: {
      "secretOtp":secretOtp,
      "enteredOtp":enteredOtp
    });

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => response.data);
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> registerUser(SignUpBodyPrem signUpBodyPrem) {
    var endpoint = "${DioProvider.baseUrl}/register/";
    var dioCall = dioClient.post(endpoint, data:signUpBodyPrem.toJson() );

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => response.data);
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> sendOtp(String phone) {
    var endpoint = "${DioProvider.baseUrl}/auth/sendOtp";
    var dioCall = dioClient.post(endpoint,queryParameters: {
      "phone":phone
    } );

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => response.data);
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> signInUser(String phone, String password) {
    var endpoint = "${DioProvider.baseUrl}/auth/";
    var dioCall = dioClient.post(endpoint,data: {
      "phone":phone,
      "password":password
    });

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => response.data);
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }



  @override
  Future<Map<String, dynamic>> checkPasswordMatch(String oldPassword) {
    var endpoint = "${DioProvider.baseUrl}/search/repositories";
    var dioCall = dioClient.get(endpoint,);

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) =>response.data);
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> updateUserPassword(UserPassBodyPrem userPassBodyPrem) {
    var endpoint = "${DioProvider.baseUrl}/register/changeUserPassword";
    var dioCall = dioClient.post(endpoint, data:userPassBodyPrem.toJson());

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => response.data);
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> checkServer() {
    var endpoint = "${DioProvider.baseUrl}/auth/testServer";
    var dioCall = dioClient.get(endpoint);

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => response.data);
    } catch (e) {
      rethrow;
    }

    throw UnimplementedError();
  }

}