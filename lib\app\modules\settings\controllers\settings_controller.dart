import 'package:get/get.dart';
import '../../../../theme.dart';
import '../../../core/base/notification_controller.dart';
import '../../../data/model/user_data.dart';
import '../../../data/repository/user_repository.dart';
import '../model/user_ui_data.dart';
import '/app/core/base/base_controller.dart';

class SettingsController extends BaseController {
  var isNotificationEnabled = false.obs; // Reactive boolean state

  void toggleNotification(bool value) {
    // isNotificationEnabled.value = value;
    // if (value) {
    //
    //   // Get the current time
    //   final now = DateTime.now();
    //
    //   // Schedule the notification 2 minutes from now
    //   NotificationApi.scheduleDailyNotification(
    //       now.hour,
    //       now.minute + 2, // Schedule 2 minutes from now
    //       'Test Reminder',
    //       'This is a test notification!'
    //   );
    // } else {
    //   // Cancel the notification if disabled
    //   NotificationApi.cancelAllNotifications();
    // }
  }

  final count = 0.obs;
  FontSizeController fontSizeController = Get.find();
  void increment() => count.value++;

  final UserRepository _repositoryUser =
      Get.find(tag: (UserRepository).toString());

  final Rx<UserUiData> _userUiDataController = Rx(UserUiData());
  UserUiData get userUiData => _userUiDataController.value;

  void getUserData() {
    var future = _repositoryUser.getUserData("");
    callDataService(future, onSuccess: _handleSuccess, onError: _handleError);
  }

  _handleSuccess(UserData response) {
    _userUiDataController(UserUiData(
      id: response.message?.id != null ? response.message?.id! : 0,
      name: response.message?.name ?? "",
      photo: response.message?.photo ?? "",
      phone: response.message?.phone ?? '0',
      subscription: response.message?.subscriptionRequest == null
          ? false
          : response.message?.subscriptionRequest!.subscribed == false
              ? false
              : true,
    ));
  }

  _handleError(Exception e) {}
}
