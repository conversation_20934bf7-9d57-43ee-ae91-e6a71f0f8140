import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../routes/app_pages.dart';
import '../model/news_list_data_model.dart';

class NewsListCard extends StatelessWidget {
  final NewsListDataModel newsListDataModel;
  const NewsListCard({Key? key, required this.newsListDataModel})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5),
            spreadRadius: 5,
            blurRadius: 7,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: ListTile(
        leading: CachedNetworkImage(
          width: 50,
          height: 50,
          imageUrl: newsListDataModel.image,
          placeholder: (context, url) => CircularProgressIndicator(),
          errorWidget: (context, url, error) => Icon(Icons.newspaper, size: 50),
        ),
        title: Text(newsListDataModel.title),
        subtitle: Text(newsListDataModel.description),
        trailing: Icon(Icons.arrow_forward_ios),
        onTap: () {
          Get.toNamed(Routes.NEWS_PAPER_READING, arguments: newsListDataModel);
        },
      ),
    );
  }
}
