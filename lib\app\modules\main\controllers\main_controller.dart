import 'package:get/get.dart';
import '../../../data/model/user_data.dart';
import '../../../data/repository/user_repository.dart';
import '/app/core/base/base_controller.dart';
import '/app/modules/main/model/menu_code.dart';

class MainController extends BaseController {
  final UserRepository _repositoryUser =
      Get.find(tag: (UserRepository).toString());

  var isSubscribed = false.obs;

  void getUserData() {
    var future = _repositoryUser.getUserData("");
    callDataService(future, onSuccess: _handleSuccess, onError: _handleError);
  }

  _handleSuccess(UserData response) {
    response.message?.subscriptionRequest == null
        ? isSubscribed.value = false
        : response.message?.subscriptionRequest!.subscribed == false
            ? isSubscribed.value = false
            : isSubscribed.value = true;
  }

  _handleError(Exception exception) {}

  final _selectedMenuCodeController = MenuCode.HOME.obs;

  MenuCode get selectedMenuCode => _selectedMenuCodeController.value;

  final lifeCardUpdateController = false.obs;

  onMenuSelected(MenuCode menuCode) async {
    _selectedMenuCodeController(menuCode);
  }

  @override
  void onInit() {
    getUserData();
    super.onInit();
  }
}
