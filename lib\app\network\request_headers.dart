import 'package:dio/dio.dart';

import '../data/local/preference/preference_manager.dart';
import 'dio_provider.dart';

class RequestHeaderInterceptor extends InterceptorsWrapper {
  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    String token = await DioProvider.preferenceManager
        .getString(PreferenceManager.keyToken);

    if (token.isNotEmpty) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    super.onRequest(options, handler);
  }
}
