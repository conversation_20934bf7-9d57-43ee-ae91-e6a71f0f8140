import 'package:flutter/cupertino.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import 'package:news_lexica_app/app/core/model/lesson_discussion_body_prem.dart';
import '../../../core/base/paging_controller.dart';
import '../../../core/model/discussion_query_prem.dart';
import '../../../data/model/lesson_discussion_response.dart';
import '../../../data/repository/lesson_repository.dart';
import '../model/comment_ui_data.dart';

enum VoteType { UPVOTE, DOWNVOTE }

class QuizDiscussionController extends BaseController {
  final LessonRepository _repository = Get.find(
    tag: (LessonRepository).toString(),
  );
  TextEditingController commentController = TextEditingController();
  int lessonId = 0;
  String replyId = '';
  final RxList<CommentUiData> _commentListController = RxList.empty();

  List<CommentUiData> get commentList => _commentListController.toList();

  addQuizComment(String comment) {
    var addComment = _repository.addLessonDiscussion(
      LessonDiscussionBodyPrem(
        comment: comment,
        lesson: lessonId.toString(),
        user: '',
      ),
    );
    callDataService(addComment, onSuccess: _handleLessonDiscussion);
  }

  addCommentReply(String comment, String commentId) {
    var addComment = _repository.addLessonDiscussion(
      LessonDiscussionBodyPrem(
        comment: comment,
        lesson: lessonId.toString(),
        user: '',
        parentId: commentId,
      ),
    );
    callDataService(addComment, onSuccess: _handleLessonDiscussion);
  }

  final pagingController = PagingController<CommentUiData>();

  getAllDiscussionByLessonId(int lessonId) {
    if (!pagingController.canLoadNextPage()) return;

    pagingController.isLoadingPage = true;

    var queryParam = DiscussionQueryPrem(
      pageNumber: pagingController.pageNumber,
      lessonId: lessonId,
      perPage: 10,
    );

    var lessonDiscussion = _repository.getAllLessonDiscussion(queryParam);
    callDataService(
      lessonDiscussion,
      onSuccess: _handleLessonDisscussionSuccess,
    );

    pagingController.isLoadingPage = false;
  }

  bool _isLastPage(int newListItemCount, int totalCount) {
    return (commentList.length + newListItemCount) >= totalCount;
  }

  List<CommentUiData> mapReplies(List<Replies>? replies) {
    if (replies == null) return [];
    return replies.map((reply) {
      final createdAt =
          reply.createdAt != null ? DateTime.parse(reply.createdAt!) : null;
      final formattedTime =
          createdAt != null
              ? DateFormat('yyyy-MM-dd – kk:mm').format(createdAt)
              : '';
      return CommentUiData(
        userName: reply.user?.name ?? "Null",
        time: formattedTime,
        comment: reply.comment ?? "Null",
        parentId: reply.parentId.toString() ?? "Null",
        userId: reply.userId?.toString() ?? "Null",
        id: reply.id ?? 0,
        // Since Replies doesn't have nested replies, we pass an empty list
        reply: [],
      );
    }).toList();
  }

  List<CommentUiData> mapComments(List<Data>? dataList) {
    if (dataList == null) return [];
    return dataList.map((e) {
      final createdAt =
          e.createdAt != null ? DateTime.parse(e.createdAt!) : null;
      final formattedTime =
          createdAt != null
              ? DateFormat('yyyy-MM-dd – kk:mm').format(createdAt)
              : '';

      // Map replies using the separate function
      final List<CommentUiData> replyList = mapReplies(e.replies);

      return CommentUiData(
        userName: e.user?.name ?? "Null",
        parentId: e.parentId ?? "Null",
        time: formattedTime,
        comment: e.comment ?? "Null",
        userId: e.userId?.toString() ?? "Null",
        id: e.id ?? 0,
        reply: replyList,
      );
    }).toList();
  }

  _handleLessonDisscussionSuccess(LessonDiscussionResponse response) {
    final List<CommentUiData> repoList = mapComments(response.data);

    if (_isLastPage(repoList.length, response.totalItems!)) {
      pagingController.appendLastPage(repoList);
    } else {
      pagingController.appendPage(repoList);
    }

    var newList = [...pagingController.listItems];

    _commentListController(newList);
  }

  onRefreshPage() {
    pagingController.initRefresh();
    getAllDiscussionByLessonId(lessonId);
  }

  onLoadNextPage() {
    logger.i("On load next");

    getAllDiscussionByLessonId(lessonId);
  }

  voteComment(id, voteType) {
    var future = _repository.voteDiscussion(id, voteType);

    callDataService(future, onSuccess: _handleVoteSuccess);
  }

  @override
  void onInit() {
    var data = Get.arguments;
    if (data is int) {
      lessonId = data;
      getAllDiscussionByLessonId(data);
    }
    super.onInit();
  }

  _handleLessonDiscussion(Map<String, dynamic> response) {
    onRefreshPage();
  }

  _handleVoteSuccess(Map<String, dynamic> response) {
    onRefreshPage();
  }
}
