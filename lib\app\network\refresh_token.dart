import 'package:dio/dio.dart';
import '../../flavors/build_config.dart';
import '../data/local/preference/preference_manager.dart';
import 'dio_provider.dart';

class TokenRefreshInterceptor extends Interceptor {
  static final String baseUrl = BuildConfig.instance.config.baseUrl;
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 403) {
      String refreshToken =
          await DioProvider.preferenceManager.getString("refreshToken");
      if (refreshToken.isNotEmpty) {
        try {
          Dio refreshDio = Dio();
          Response response = await refreshDio.post(
            '${baseUrl}/auth/newAccessToken',
            data: {'refreshToken': refreshToken},
          );

          String newAccessToken = response.data['accessToken'];
          String newRefreshToken = response.data['newRefreshToken'];

          await DioProvider.preferenceManager
              .setString(PreferenceManager.keyToken, newAccessToken);
          await DioProvider.preferenceManager
              .setString(PreferenceManager.refreshToken, newRefreshToken);

          err.requestOptions.headers['Authorization'] =
              'Bearer $newAccessToken';
          final retryResponse =
              await DioProvider.dioWithHeaderToken.fetch(err.requestOptions);
          return handler.resolve(retryResponse);
        } catch (e) {
          return handler.next(err);
        }
      }
    }
    super.onError(err, handler);
  }
}
