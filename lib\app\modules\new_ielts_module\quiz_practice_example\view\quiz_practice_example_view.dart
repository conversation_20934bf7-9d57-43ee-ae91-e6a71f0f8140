import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/widget/custom_app_bar.dart';

import '../../../../data/model/reading_passage_details.dart';
import '../controller/quiz_practice_exmple_controller.dart';

class QuizPracticeExampleView extends BaseView<QuizPracticeExampleController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return CustomAppBar(appBarTitleText: 'Vocabulary from Reading Passage-1 ');
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return Obx(() => IeltsReadingTable(
          lessonList: controller.exampleList,
        ));
    throw UnimplementedError();
  }
}

class IeltsReadingTable extends StatelessWidget {
  final List<IeltsReadingPassageExample> lessonList;

  IeltsReadingTable({Key? key, required this.lessonList}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          // Sticky Table Header
          Container(
            decoration: BoxDecoration(
              color: Color(0xff000000),
              border: Border.all(color: Color(0xff000000), width: 1),
            ),
            child: Row(
              children: [
                _buildTableCell('Word', isHeader: true),
                _buildTableCell('Synonym', isHeader: true),
                _buildTableCell('Antonym', isHeader: true),
              ],
            ),
          ),

          // Scrollable Table Body
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: lessonList.map((item) {
                  return Row(
                    children: [
                      _buildTableCell(item.word ?? '', isHeader: false),
                      _buildTableCell(item.synonym ?? '', isHeader: false),
                      _buildTableCell(item.antonym ?? '', isHeader: false),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Function for creating table cells with equal height
  Widget _buildTableCell(String text, {bool isHeader = false}) {
    return Expanded(
      child: Container(
        height: 60, // Ensure all cells have equal height
        alignment: Alignment.center,
        padding: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          border: Border.all(
              color: Color(0xfffd9800), width: 1), // Ensuring all borders
          color: isHeader ? Color(0xfffd9800) : Colors.white,
        ),
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontWeight: isHeader ? FontWeight.bold : FontWeight.normal,
            color: isHeader ? Colors.white : Colors.black,
          ),
        ),
      ),
    );
  }
}
