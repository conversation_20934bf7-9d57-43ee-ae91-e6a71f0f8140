import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/widget/elevated_container.dart';
import 'package:news_lexica_app/app/routes/app_pages.dart';

import '../../../../core/values/app_values.dart';
import '../model/book_data_model.dart';

class ReadingListCard extends StatelessWidget {
  const ReadingListCard({
    Key? key,
    required this.book,
  }) : super(key: key);

  final ReadingListModel book;

  @override
  Widget build(BuildContext context) {
    return ElevatedContainer(
      bgColor: Theme.of(context).cardColor,
      child: Padding(
        padding: const EdgeInsets.all(AppValues.padding),
        child: ListTile(
          onTap: () {
            Get.toNamed(Routes.NEW_IELTS_READING_PASSAGE_LIST,
                arguments: book.id);
          },
          leading: Image.asset("assets/exam.png"),
          title:
              Text(book.title, style: Theme.of(context).textTheme.titleMedium),
          trailing: IconButton(
            icon: Icon(Icons.chevron_right),
            onPressed: () {
              Get.toNamed(Routes.NEW_IELTS_READING_PASSAGE_LIST,
                  arguments: book.id);
            },
          ),
        ),
      ),
    );
  }
}
