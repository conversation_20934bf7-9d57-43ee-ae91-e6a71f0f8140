import 'dart:ui';
import 'package:news_lexica_app/app/core/model/main_category.dart';

List<MainCategory> mainCategory = [
  MainCategory(
      title: "NEWS",
      subtitle: "Article and quiz's on News",
      assets: 'assets/news.png'),
  MainCategory(
      title: "LIFESTYLE & CAREER",
      subtitle: "Article and quiz's on Life Style & Fashion",
      assets: 'assets/life.png'),
  MainCategory(
      title: "OPINION",
      subtitle: "Article and quiz's on Opinion",
      assets: 'assets/opinion.png'),
  MainCategory(
      title: "POLITICS",
      subtitle: "Article and quiz's on Politics",
      assets: 'assets/politics.png'),
  MainCategory(
      title: "BUSINESS & ECONOMICS",
      subtitle: "Article and quiz's on Business & Economics",
      assets: 'assets/business.png'),
  MainCategory(
      title: "TECH",
      subtitle: "Article and quiz's on Tech",
      assets: 'assets/tech.png'),
  MainCategory(
      title: "SCIENCE",
      subtitle: "Article and quiz's on Science",
      assets: 'assets/science.png'),
  MainCategory(
      title: "CLIMATE & ENVIRONMENT",
      subtitle: "Article and quiz's on Climate & Entertainment",
      assets: 'assets/climate.png'),
  MainCategory(
      title: "SPORTS",
      subtitle: "Article and quiz's on Sports",
      assets: 'assets/football.png'),
  MainCategory(
      title: "HEALTH",
      subtitle: "Article and quiz's on Health",
      assets: 'assets/health.png'),
  MainCategory(
      title: "ENTERTAINMENT",
      subtitle: "Article and quiz's on Entertainment",
      assets: 'assets/entertainment.png'),
  MainCategory(
      title: "BOOKS",
      subtitle: "Article and quiz's on Book Preview",
      assets: 'assets/book.png'),
  MainCategory(
      title: "WEATHER",
      subtitle: "Article and quiz's on Whether",
      assets: 'assets/weather.png'),
  MainCategory(
      title: "LAW AND RIGHTS",
      subtitle: "Article and quiz's on LAW AND RIGHTS",
      assets: 'assets/compliant.png'),
  MainCategory(
      title: "OBITUARIES",
      subtitle: "Article and quiz's on OBITUARIES ",
      assets: 'assets/black-ribbon.png'),
  MainCategory(
      title: "EDITORIALS",
      subtitle: "Article and quiz's on EDITORIALS",
      assets: 'assets/editorial.png'),
];

String getAssetByTitle(String title) {
  switch (title) {
    case "NEWS":
      return 'assets/news.png';
    case "LIFESTYLE & CAREER":
      return 'assets/life.png';
    case "OPINION":
      return 'assets/opinion.png';
    case "POLITICS":
      return 'assets/politics.png';
    case "BUSINESS & ECONOMICS":
      return 'assets/business.png';
    case "TECH":
      return 'assets/tech.png';
    case "SCIENCE":
      return 'assets/science.png';
    case "CLIMATE & ENVIRONMENT":
      return 'assets/climate.png';
    case "SPORTS":
      return 'assets/football.png';
    case "HEALTH":
      return 'assets/health.png';
    case "ENTERTAINMENT":
      return 'assets/entertainment.png';
    case "BOOKS":
      return 'assets/book.png';
    case "WEATHER":
      return 'assets/weather.png';
    case "EDITORIALS":
      return 'assets/editorial.png';
    default:
      return "assets/editorial.png"; // Handle if title doesn't match any category
  }
}

List<List<Color>> lightColors = const [
  [Color(0xff383838), Color(0xff3B3B3B)],
  [Color(0xff0A0E14), Color(0xff283E51)],
  [Color(0xff1D2B64), Color(0xffF8CDDA)],
  [Color(0xff7F8C8D), Color(0xff6B6E70)],
  [Color(0xff574249), Color(0xffF9BC16)],
  [Color(0xff4B79A1), Color(0xff283E51)],
  [Color(0xff141E30), Color(0xff243B55)],
  [Color(0xffBDC3C7), Color(0xff2C3E50)],
  [Color(0xff1F1C2C), Color(0xff928DAB)],
  [Color(0xff121212), Color(0xff222831)],
  [Color(0xff0F2027), Color(0xff203A43)],
  [Color(0xff0F2027), Color(0xff2C5364)],
  [Color(0xff090909), Color(0xff283E51)],
  [Color(0xff283E51), Color(0xff4B79A1)],
  [Color(0xff333333), Color(0xff666666)],
  [Color(0xff4B79A1), Color(0xff283E51)],
  [Color(0xff141E30), Color(0xff243B55)],
  [Color(0xffBDC3C7), Color(0xff2C3E50)],
  [Color(0xff0A0E14), Color(0xff283E51)],
  [Color(0xff1D2B64), Color(0xffF8CDDA)],
  [Color(0xff7F8C8D), Color(0xff6B6E70)],
  [Color(0xff574249), Color(0xffF9BC16)],
  [Color(0xff4B79A1), Color(0xff283E51)],
];

List<List<Color>> darkColors = const [
  [Color(0xff383838), Color(0xff3B3B3B)],
  [Color(0xff0A0E14), Color(0xff283E51)],
  [Color(0xff1D2B64), Color(0xffF8CDDA)],
  [Color(0xff7F8C8D), Color(0xff6B6E70)],
  [Color(0xff574249), Color(0xffF9BC16)],
  [Color(0xff4B79A1), Color(0xff283E51)],
  [Color(0xff141E30), Color(0xff243B55)],
  [Color(0xffBDC3C7), Color(0xff2C3E50)],
  [Color(0xff1F1C2C), Color(0xff928DAB)],
  [Color(0xff121212), Color(0xff222831)],
  [Color(0xff0F2027), Color(0xff203A43)],
  [Color(0xff0F2027), Color(0xff2C5364)],
  [Color(0xff090909), Color(0xff283E51)],
  [Color(0xff283E51), Color(0xff4B79A1)],
  [Color(0xff333333), Color(0xff666666)],
  [Color(0xff1D1F20), Color(0xff0D2C54)],
  [Color(0xff202329), Color(0xff393F4D)],
  [Color(0xff434343), Color(0xff000000)],
  [Color(0xff1D1E1F), Color(0xff333533)],
  [Color(0xff1A1A1A), Color(0xff2F3C4C)],
  [Color(0xff1A1A1D), Color(0xff262A2F)],
  [Color(0xff1E1E1E), Color(0xff282C34)],
  [Color(0xff101010), Color(0xff303030)],
  [Color(0xff181818), Color(0xff1F2124)],
  [Color(0xff232526), Color(0xff414345)],
  [Color(0xff292C34), Color(0xff1E2832)],
  [Color(0xff1E1E1E), Color(0xff303030)],
];
