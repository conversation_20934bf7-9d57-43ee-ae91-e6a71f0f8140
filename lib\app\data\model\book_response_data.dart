class BookResponseData {
  List<BooksList>? booksList;

  BookResponseData({this.booksList});

  BookResponseData.fromJson(Map<String, dynamic> json) {
    if (json['booksList'] != null) {
      booksList = <BooksList>[];
      json['booksList'].forEach((v) {
        booksList!.add(new BooksList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.booksList != null) {
      data['booksList'] = this.booksList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class BooksList {
  int? id;
  String? name;
  String? subtitle;
  String? createdAt;
  String? updatedAt;

  BooksList(
      {this.id, this.name, this.subtitle, this.createdAt, this.updatedAt});

  BooksList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    subtitle = json['subtitle'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['subtitle'] = this.subtitle;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    return data;
  }
}
