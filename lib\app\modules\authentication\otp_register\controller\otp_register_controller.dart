import 'dart:async';
import 'package:news_lexica_app/app/core/base/base_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/model/sign_up_data.dart';
import 'package:news_lexica_app/app/routes/app_pages.dart';
import '../../../../core/model/signup_body_prem.dart';
import '../../../../data/repository/authentication_repository.dart';

class OtpRegisterController extends BaseController {
  var signUpData = SignUpData();
  var otpController = TextEditingController();
  var secondsRemaining = 60.obs;
  final AuthenticationRepository _repository = Get.find(
    tag: (AuthenticationRepository).toString(),
  );
  var secretOtp = '';
  void startTimer() {
    Timer.periodic(Duration(seconds: 1), (timer) {
      if (secondsRemaining.value > 0) {
        secondsRemaining.value--;
      } else {
        timer.cancel();
      }
    });
  }

  signUpUser() {
    var signUpUserService = _repository.registerUser(
      SignUpBodyPrem(signUpData.name!, signUpData.phone!, signUpData.password!),
    );
    callDataService(
      signUpUserService,
      onSuccess: _handleSignUpSuccess,
      onError: _handleSignUpError,
    );
  }

  checkOtpMatch() {
    var checkOtpService = _repository.matchOtp(otpController.text, secretOtp);
    callDataService(
      checkOtpService,
      onSuccess: _handleOtpSuccess,
      onError: _handleSignUpError,
    );
  }

  sendOtp() {
    var sendOtpService = _repository.sendOtp(signUpData.phone!);
    callDataService(
      sendOtpService,
      onSuccess: _handleOtpSendSuccess,
      onError: _handleOtpError,
    );
  }

  @override
  void onInit() {
    startTimer();
    var dataModel = Get.arguments;
    if (dataModel is SignUpData) {
      signUpData = dataModel;
      secretOtp = "${signUpData.otp}";
    }
    super.onInit();
  }

  _handleSignUpSuccess(Map<String, dynamic> response) {
    Get.toNamed(Routes.MAIN);
  }

  _handleSignUpError(Exception exception) {}

  _handleOtpSuccess(Map<String, dynamic> response) {
    signUpUser();
  }

  _handleOtpError(Exception exception) {}

  _handleOtpSendSuccess(Map<String, dynamic> response) {
    secondsRemaining.value = 60;
    startTimer();
    signUpData.otp = response['message'];
    secretOtp = response['message'];
  }
}
