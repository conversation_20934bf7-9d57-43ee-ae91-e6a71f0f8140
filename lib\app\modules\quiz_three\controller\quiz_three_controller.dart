import 'dart:math';
import 'package:audioplayers/audioplayers.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import '../../../core/model/quiz.dart';
import '../../../core/base/base_controller.dart';
import '../../../core/model/update_user_score_body_prem.dart';
import '../../../data/repository/user_repository.dart';
import '../../lesson_details/model/quiz_three_ui_data.dart';

enum Answer { right, wrong, pending }

class QuizThreeController extends BaseController {
  final UserRepository _repositoryUser =
      Get.find(tag: (UserRepository).toString());
  var startIndex = 0.obs;
  var endIndex = 3.obs;
  var list = <Answer>[].obs;

  var currentIndex = 0.obs;
  var quiz3 = <Quiz>[].obs;

  Random random = Random();
  final player = AudioPlayer();
  getNextQuestion() {
    startIndex.value = endIndex.value;
    endIndex.value = (endIndex.value + 3).clamp(0, quiz3.length);
  }

  clearQuiz() {
    startIndex.value = 0;
    endIndex.value = 5;
    currentIndex.value = 0;
  }

  previousQuestion() {
    endIndex.value = startIndex.value;
    startIndex.value = (startIndex.value - 3).clamp(0, quiz3.length);
  }

  bool isChoiceSelected(int questionIndex, int choiceIndex) {
    return quiz3[questionIndex].selectedChoice == choiceIndex;
  }

  void toggleChoice(int questionIndex, int choiceIndex) {
    final question = quiz3[questionIndex];
    question.selectedChoice = choiceIndex;
  }

  String getQuizImage(int score, int quizSize) {
    var percentage = (score / quizSize) * 100;
    if (percentage > 80) {
      return "assets/gold.png";
    } else if (percentage > 60) {
      return "assets/silver.png";
    }
    if (percentage > 30) {
      return "assets/bronze.png";
    } else {
      return "assets/failed.png";
    }
  }

  String getQuizText(int score, int quizSize) {
    var percentage = (score / quizSize) * 100;
    if (percentage > 80) {
      return "Brilliant Work!";
    } else if (percentage > 60) {
      return "Excellent Work!";
    }
    if (percentage > 30) {
      return "Good Work";
    } else {
      return "Try again latter";
    }
  }

  updateScore(String score) {
    var updateScoreService = _repositoryUser
        .updateUserScore(UpdateUserScoreBodyPrem(userId: "", score: score));
    callDataService(updateScoreService,
        onSuccess: _handleScoreUpdateSuccess, onError: _handleScoreUpdateError);
  }

  _handleScoreUpdateSuccess(Map<String, dynamic> response) {
    Get.back();
    Get.back();
  }

  _handleScoreUpdateError(Exception exception) {
    Fluttertoast.showToast(msg: errorMessage);
    Get.back();
    Get.back();
  }

  resetQuiz() {
    currentIndex.value = 0;
  }

  nextQuestion() => currentIndex++;

  void initializeQuizzes() {
    quiz3.shuffle();
  }

  bool quizAvailable() {
    if (currentIndex.value + 1 >= quiz3.length) {
      return false;
    } else {
      return true;
    }
  }

  bool quizCheckAnswer(String answer, Quiz quiz) {
    return quiz.answer.toString().toLowerCase().trim() ==
            answer.toLowerCase().trim()
        ? true
        : false;
  }

  getLesson(List<QuizThreeUiData> lesson) {
    lesson.forEach((element) {
      quiz3.add(Quiz(choices: [
        element.option1,
        element.option2,
        element.option3,
        element.option4
      ], question: element.question, answer: element.answer));
    });
  }

  @override
  void onInit() {
    var dataModel = Get.arguments;
    if (dataModel is List<QuizThreeUiData>) {
      getLesson(dataModel);
      list.value = List.generate(dataModel.length, (index) => Answer.pending);
    }
    super.onInit();
  }
}
