import 'package:news_lexica_app/app/core/base/base_controller.dart';

import '../model/news_list_data_model.dart';

class NewsListController extends BaseController {
  List<NewsListDataModel> newsList = [
    NewsListDataModel(
      title: 'The New York Times(USA)',
      description: '',
      url: 'https://www.nytimes.com/international/',
      image:
          'https://icons.iconarchive.com/icons/martz90/circle-addon1/256/new-york-times-icon.png',
    ),
    NewsListDataModel(
      title: 'The Wall Street Journal (USA)',
      description: '',
      url: 'https://www.wsj.com/',
      image: 'https://www.wsj.com/apple-touch-icon.png',
    ),
    NewsListDataModel(
      title: 'The Washington Post (USA)',
      description: '',
      url: 'https://www.washingtonpost.com/',
      image:
          'https://icon-icons.com/icons2/2699/PNG/512/washingtonpost_logo_icon_170226.png',
    ),
    NewsListDataModel(
      title: 'USA Today (USA)',
      description: '',
      url: 'https://www.usatoday.com/',
      image:
          'https://icon-icons.com/icons2/2699/PNG/512/usatoday_logo_icon_170309.png',
    ),
    NewsListDataModel(
      title: 'Los Angeles Times (USA)',
      description: '',
      url: 'https://www.usatoday.com/',
      image: 'https://www.gannett-cdn.com/-mm-/b1f',
    ),
    NewsListDataModel(
      title: 'CNN (USA)',
      description: '',
      url: 'https://www.cnn.com/',
      image: 'https://www.cnn.com/favicon.ico',
    ),
    NewsListDataModel(
      title: 'Bloomberg (USA)',
      description: '',
      url: 'https://www.bloomberg.com/',
      image:
          'https://assets.bwbx.io/s3/javelin/public/javelin/images/favicon.ico',
    ),
    NewsListDataModel(
      title: 'Associated Press  (USA)',
      description: '',
      url: 'https://apnews.com/',
      image:
          'https://apnews.com/resources/assets/website/2.0.0/images/ap-logo-180x180.png',
    ),
    NewsListDataModel(
      title: 'The Daily Mail (UK)',
      description: '',
      url: 'https://www.dailymail.co.uk/home/<USER>',
      image:
          'https://apnews.com/resources/assets/website/2.0.0/images/ap-logo-180x180.png',
    ),
    NewsListDataModel(
      title: 'The Sun (UK)',
      description: '',
      url: 'https://www.thesun.co.uk/',
      image:
          'https://apnews.com/resources/assets/website/2.0.0/images/ap-logo-180x180.png',
    ),
    NewsListDataModel(
      title: 'The Telegraph (UK)',
      description: '',
      url: 'https://www.telegraph.co.uk/',
      image:
          'https://apnews.com/resources/assets/website/2.0.0/images/ap-logo-180x180.png',
    ),
    NewsListDataModel(
      title: 'The Telegraph (UK)',
      description: '',
      url: 'https://www.telegraph.co.uk/',
      image:
          'https://apnews.com/resources/assets/website/2.0.0/images/ap-logo-180x180.png',
    ),
    NewsListDataModel(
      title: 'The Times (UK)',
      description: '',
      url: 'https://www.thetimes.com/',
      image:
          'https://apnews.com/resources/assets/website/2.0.0/images/ap-logo-180x180.png',
    ),
    NewsListDataModel(
      title: 'The Guardian (UK)',
      description: '',
      url: 'https://www.theguardian.com/',
      image:
          'https://apnews.com/resources/assets/website/2.0.0/images/ap-logo-180x180.png',
    ),
    NewsListDataModel(
      title: 'Financial Times (UK)',
      description: '',
      url: 'https://www.ft.com/',
      image:
          'https://apnews.com/resources/assets/website/2.0.0/images/ap-logo-180x180.png',
    ),
    NewsListDataModel(
      title: 'The Independent (UK)',
      description: '',
      url: 'https://www.independent.co.uk/',
      image:
          'https://apnews.com/resources/assets/website/2.0.0/images/ap-logo-180x180.png',
    ),
    NewsListDataModel(
      title: 'BBC News (UK)',
      description: '',
      url: 'https://www.bbc.com/news',
      image:
          'https://apnews.com/resources/assets/website/2.0.0/images/ap-logo-180x180.png',
    ),
    NewsListDataModel(
      title: 'The Economist (UK)',
      description: '',
      url: 'https://www.economist.com/',
      image:
          'https://apnews.com/resources/assets/website/2.0.0/images/ap-logo-180x180.png',
    ),
    NewsListDataModel(
      title: 'Reuters  (UK) ',
      description: '',
      url: 'https://www.reuters.com/',
      image:
          'https://apnews.com/resources/assets/website/2.0.0/images/ap-logo-180x180.png',
    ),
    NewsListDataModel(
      title: 'The Daily Star (Bangladesh)',
      description: '',
      url: 'https://www.thedailystar.net/',
      image:
          'https://apnews.com/resources/assets/website/2.0.0/images/ap-logo-180x180.png',
    ),
    NewsListDataModel(
      title: 'Dhaka Tribune (Bangladesh)',
      description: '',
      url: 'https://www.dhakatribune.com/',
      image: 'https://example.com/dhaka-tribune-icon.png',
    ),
    NewsListDataModel(
      title: 'The Financial Express (Bangladesh)',
      description: '',
      url: 'https://thefinancialexpress.com.bd/',
      image: 'https://example.com/financial-express-icon.png',
    ),
    NewsListDataModel(
      title: 'Daily Observer (Bangladesh)',
      description: '',
      url: 'https://www.observerbd.com/',
      image: 'https://example.com/daily-observer-icon.png',
    ),
    NewsListDataModel(
      title: 'New Age (Bangladesh)',
      description: '',
      url: 'https://www.newagebd.net/',
      image: 'https://example.com/new-age-icon.png',
    ),
    NewsListDataModel(
      title: 'The Business Standard (Bangladesh)',
      description: '',
      url: 'https://www.tbsnews.net/',
      image: 'https://example.com/business-standard-icon.png',
    ),
    NewsListDataModel(
      title: 'The Hindu (India)',
      description: '',
      url: 'https://www.thehindu.com/',
      image: 'https://example.com/business-standard-icon.png',
    ),
    NewsListDataModel(
      title: 'The Times of India (India)',
      description: '',
      url: 'https://timesofindia.indiatimes.com/',
      image: 'https://example.com/business-standard-icon.png',
    ),
    NewsListDataModel(
      title: 'Hindustan Times  (India)',
      description: '',
      url: 'https://www.hindustantimes.com/',
      image: 'https://example.com/business-standard-icon.png',
    ),
    NewsListDataModel(
      title: 'The Indian Express (India)',
      description: '',
      url: 'https://indianexpress.com/international/',
      image: 'https://example.com/business-standard-icon.png',
    ),
    NewsListDataModel(
      title: 'The Economic Times (India)',
      description: '',
      url: 'https://m.economictimes.com/',
      image: 'https://example.com/business-standard-icon.png',
    ),
    NewsListDataModel(
      title: 'Deccan Chronicle (India)',
      description: '',
      url: 'https://www.deccanchronicle.com/',
      image: 'https://example.com/deccan-chronicle-icon.png',
    ),
    NewsListDataModel(
      title: 'Business Standard (India)',
      description: '',
      url: 'https://www.business-standard.com/',
      image: 'https://example.com/business-standard-icon.png',
    ),
    NewsListDataModel(
      title: 'Times Now (India)',
      description: '',
      url: 'https://www.timesnownews.com/',
      image: 'https://example.com/times-now-icon.png',
    ),
    NewsListDataModel(
      title: 'ESPNcricinfo (India)',
      description: '',
      url: 'https://www.espncricinfo.com/',
      image: 'https://example.com/espncricinfo-icon.png',
    ),
    NewsListDataModel(
      title: 'Cricbuzz (India)',
      description: '',
      url: 'https://www.cricbuzz.com/',
      image: 'https://example.com/cricbuzz-icon.png',
    ),
    NewsListDataModel(
      title: 'Le Monde (France)',
      description: '',
      url: 'https://www.lemonde.fr/en/',
      image: 'https://example.com/le-monde-icon.png',
    ),
    NewsListDataModel(
      title: 'Deutsche Welle (Germany)',
      description: '',
      url: 'https://www.dw.com/',
      image: 'https://example.com/deutsche-welle-icon.png',
    ),
    NewsListDataModel(
      title: 'Asahi Shimbun (Japan)',
      description: '',
      url: 'https://www.asahi.com/',
      image: 'https://example.com/asahi-shimbun-icon.png',
    ),
    NewsListDataModel(
      title: 'The Japan Times (Japan)',
      description: '',
      url: 'https://www.japantimes.co.jp/',
      image: 'https://example.com/japan-times-icon.png',
    ),
    NewsListDataModel(
      title: 'The Sydney Morning Herald (Australia)',
      description: '',
      url: 'https://www.smh.com.au/',
      image: 'https://example.com/sydney-morning-herald-icon.png',
    ),
    NewsListDataModel(
      title: 'The Straits Times (Singapore)',
      description: '',
      url: 'https://www.straitstimes.com/',
      image: 'https://example.com/straits-times-icon.png',
    ),
    NewsListDataModel(
      title: 'South China Morning Post (Hong Kong)',
      description: '',
      url: 'https://www.scmp.com/',
      image: 'https://example.com/south-china-morning-post-icon.png',
    ),
    NewsListDataModel(
      title: 'The Korea Herald (South Korea)',
      description: '',
      url: 'https://www.koreaherald.com/',
      image: 'https://example.com/korea-herald-icon.png',
    ),
    NewsListDataModel(
      title: 'Al Jazeera (Qatar)',
      description: '',
      url: 'https://www.aljazeera.com/',
      image: 'https://example.com/al-jazeera-icon.png',
    ),
    NewsListDataModel(
      title: 'Dawn (Pakistan)',
      description: '',
      url: 'https://www.dawn.com/',
      image: 'https://example.com/dawn-icon.png',
    ),
    NewsListDataModel(
      title: 'The Express Tribune (Pakistan)',
      description: '',
      url: 'https://tribune.com.pk/',
      image: 'https://example.com/express-tribune-icon.png',
    ),
  ];

  List<NewsListDataModel> magazineList = [
    NewsListDataModel(
      title: 'Astronomy',
      description: 'Celestial events, space exploration, and astrophotography.',
      url: 'https://www.astronomy.com/',
      image: 'https://example.com/astronomy-icon.png',
    ),
    NewsListDataModel(
      title: 'BBC Science Focus',
      description:
          'Scientific discoveries, technological advancements, and health innovations.',
      url: 'https://www.sciencefocus.com/',
      image: 'https://example.com/bbc-science-focus-icon.png',
    ),
    NewsListDataModel(
      title: 'Bloomberg Businessweek',
      description:
          'Insights and in-depth analysis on people, companies, events, and trends.',
      url: 'https://www.bloomberg.com/businessweek',
      image: 'https://example.com/bloomberg-businessweek-icon.png',
    ),
    NewsListDataModel(
      title: 'Business Insider',
      description: 'Business, technology, finance, and lifestyle.',
      url: 'https://www.businessinsider.com/',
      image: 'https://example.com/business-insider-icon.png',
    ),
    NewsListDataModel(
      title: 'Cosmopolitan',
      description: 'Fashion, beauty, and relationships.',
      url: 'https://www.cosmopolitan.com/',
      image: 'https://example.com/cosmopolitan-icon.png',
    ),
    NewsListDataModel(
      title: 'Discover',
      description: 'Health, technology, space, and the environment.',
      url: 'https://www.discovermagazine.com/',
      image: 'https://example.com/discover-icon.png',
    ),
    NewsListDataModel(
      title: 'ESPN',
      description: 'Athlete profiles and coverage of major sports events.',
      url: 'https://www.espn.com/',
      image: 'https://example.com/espn-icon.png',
    ),
    NewsListDataModel(
      title: 'Esquire',
      description: 'Men\'s style, culture, and politics.',
      url: 'https://www.esquire.com/',
      image: 'https://example.com/esquire-icon.png',
    ),
    NewsListDataModel(
      title: 'Fast Company',
      description:
          'Highlights innovation in technology, leadership, and design.',
      url: 'https://www.fastcompany.com/',
      image: 'https://example.com/fast-company-icon.png',
    ),
    NewsListDataModel(
      title: 'Forbes',
      description: 'Business, finance, and technology.',
      url: 'https://www.forbes.com/',
      image: 'https://example.com/forbes-icon.png',
    ),
    NewsListDataModel(
      title: 'Foreign Policy',
      description: 'International relations and global affairs.',
      url: 'https://foreignpolicy.com/',
      image: 'https://example.com/foreign-policy-icon.png',
    ),
    NewsListDataModel(
      title: 'Fortune',
      description: 'Business, finance, and economics.',
      url: 'https://fortune.com/',
      image: 'https://example.com/fortune-icon.png',
    ),
    NewsListDataModel(
      title: 'GQ',
      description: 'Men\'s style, culture, and lifestyle.',
      url: 'https://www.gq.com/',
      image: 'https://example.com/gq-icon.png',
    ),
    NewsListDataModel(
      title: 'Hack Spirit',
      description:
          'Self-improvement, mindfulness, relationships, and personal growth.',
      url: 'https://hackspirit.com/',
      image: 'https://example.com/hack-spirit-icon.png',
    ),
    NewsListDataModel(
      title: 'Harper\'s Bazaar',
      description: 'Fashion, beauty, and culture.',
      url: 'https://www.harpersbazaar.com/',
      image: 'https://example.com/harpers-bazaar-icon.png',
    ),
    NewsListDataModel(
      title: 'Harvard Business School Online',
      description: 'Business, leadership, and management.',
      url: 'https://online.hbs.edu/',
      image: 'https://example.com/harvard-business-school-icon.png',
    ),
    NewsListDataModel(
      title: 'Inc.',
      description:
          'Insights, advice, and stories about startups, innovation, and growing companies.',
      url: 'https://www.inc.com/',
      image: 'https://example.com/inc-icon.png',
    ),
    NewsListDataModel(
      title: 'Leaders',
      description:
          'Executive strategies, management, and organizational success.',
      url: 'https://www.leaders.com/',
      image: 'https://example.com/leaders-icon.png',
    ),
    NewsListDataModel(
      title: 'Mad Magazine',
      description: 'Satire on pop culture, politics, and everyday life.',
      url: 'https://www.madmagazine.com/',
      image: 'https://example.com/mad-magazine-icon.png',
    ),
    NewsListDataModel(
      title: 'Men\'s Health',
      description: 'Health, fitness, and lifestyle.',
      url: 'https://www.menshealth.com/',
      image: 'https://example.com/mens-health-icon.png',
    ),
    NewsListDataModel(
      title: 'National Geographic',
      description: 'Science, exploration, and photography.',
      url: 'https://www.nationalgeographic.com/',
      image: 'https://example.com/national-geographic-icon.png',
    ),
    NewsListDataModel(
      title: 'Nature',
      description: 'Biology, physics, chemistry, and environmental science.',
      url: 'https://www.nature.com/',
      image: 'https://example.com/nature-icon.png',
    ),
    NewsListDataModel(
      title: 'New Scientist',
      description:
          'Discoveries and ideas in physics, biology, space exploration, and environmental science.',
      url: 'https://www.newscientist.com/',
      image: 'https://example.com/new-scientist-icon.png',
    ),
    NewsListDataModel(
      title: 'Psychology Today',
      description:
          'Mental health, psychology, relationships, and self-improvement.',
      url: 'https://www.psychologytoday.com/',
      image: 'https://example.com/psychology-today-icon.png',
    ),
    NewsListDataModel(
      title: 'Reader\'s Digest',
      description: 'Health, lifestyle, humor, and inspiring stories.',
      url: 'https://www.rd.com/',
      image: 'https://example.com/readers-digest-icon.png',
    ),
    NewsListDataModel(
      title: 'Rolling Stone',
      description: 'Music, culture, and politics.',
      url: 'https://www.rollingstone.com/',
      image: 'https://example.com/rolling-stone-icon.png',
    ),
    NewsListDataModel(
      title: 'Scientific American',
      description: 'Science and technology news.',
      url: 'https://www.scientificamerican.com/',
      image: 'https://example.com/scientific-american-icon.png',
    ),
    NewsListDataModel(
      title: 'Smithsonian',
      description: 'History, science, and culture.',
      url: 'https://www.smithsonianmag.com/',
      image: 'https://example.com/smithsonian-icon.png',
    ),
    NewsListDataModel(
      title: 'The Atlantic',
      description: 'Culture, politics, and ideas.',
      url: 'https://www.theatlantic.com/',
      image: 'https://example.com/the-atlantic-icon.png',
    ),
    NewsListDataModel(
      title: 'The Economist',
      description: 'Global politics, business, and finance.',
      url: 'https://www.economist.com/',
      image: 'https://example.com/the-economist-icon.png',
    ),
    NewsListDataModel(
      title: 'The New Yorker',
      description: 'Literature, culture, and current affairs.',
      url: 'https://www.newyorker.com/',
      image: 'https://example.com/the-new-yorker-icon.png',
    ),
    NewsListDataModel(
      title: 'Time',
      description: 'News and current affairs.',
      url: 'https://time.com/',
      image: 'https://example.com/time-icon.png',
    ),
    NewsListDataModel(
      title: 'Vanity Fair',
      description: 'Culture, politics, and lifestyle.',
      url: 'https://www.vanityfair.com/',
      image: 'https://example.com/vanity-fair-icon.png',
    ),
    NewsListDataModel(
      title: 'Vogue',
      description: 'Fashion, beauty, and culture.',
      url: 'https://www.vogue.com/',
      image: 'https://example.com/vogue-icon.png',
    ),
    NewsListDataModel(
      title: 'Wired',
      description: 'Technology, science, and culture.',
      url: 'https://www.wired.com/',
      image: 'https://example.com/wired-icon.png',
    ),
  ];
}
