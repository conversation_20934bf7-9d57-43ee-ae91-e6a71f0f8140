import 'package:flutter/material.dart';
import 'package:news_lexica_app/app/core/values/app_values.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/quiz1/model/ielts_quiz_1_ui_data.dart';

class WordCard extends StatelessWidget {
  const WordCard({
    super.key,
    required this.data,
    required this.isSelected,
    required this.onTap,
  });
  final IeltsQuiz1UiData data;
  final bool isSelected;
  final VoidCallback onTap;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.symmetric(vertical: AppValues.margin_10),
        height: AppValues.height_100,
        width: AppValues.size_200,
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue : Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: Colors.grey),
        ),
        child: Center(child: Text(data.word)),
      ),
    );
  }
}
