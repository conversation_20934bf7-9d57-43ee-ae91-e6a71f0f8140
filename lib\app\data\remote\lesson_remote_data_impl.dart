import 'dart:convert';

import 'package:dio/src/response.dart';
import 'package:dio_cache_interceptor/dio_cache_interceptor.dart';
import 'package:news_lexica_app/app/core/base/base_remote_source.dart';
import 'package:news_lexica_app/app/core/model/lesson_discussion_body_prem.dart';
import 'package:news_lexica_app/app/data/model/book_response_data.dart';
import 'package:news_lexica_app/app/data/model/exam_quiz_list.dart';
import 'package:news_lexica_app/app/data/model/gre_quiz_list.dart';
import 'package:news_lexica_app/app/data/model/lesson_discussion_response.dart';
import 'package:news_lexica_app/app/data/model/promotion_data_response.dart';
import 'package:news_lexica_app/app/data/model/reading_passage_details.dart';
import 'package:news_lexica_app/app/data/model/reading_passage_response_data.dart';
import 'package:news_lexica_app/app/data/model/reading_response_data.dart';
import 'package:news_lexica_app/app/data/remote/lesson_remote_data_source.dart';
import 'package:news_lexica_app/app/data/model/lesson_data_details.dart';
import 'package:news_lexica_app/app/data/model/lesson_data.dart';
import '../../core/model/discussion_query_prem.dart';
import '../../core/model/lesson_search_query_parm.dart';
import '../../network/dio_provider.dart';

final options = CacheOptions(
  store: MemCacheStore(),
  policy: CachePolicy.request,
  hitCacheOnErrorExcept: [401, 403],
  maxStale: const Duration(days: 7),
  priority: CachePriority.normal,
  cipher: null,
  keyBuilder: CacheOptions.defaultCacheKeyBuilder,
  allowPostMethod: false,
);

class LessonRemoteDataSrcImpl extends BaseRemoteSource
    implements LessonRemoteDataSrc {
  @override
  Future<LessonDetailsData> getLessonDataDetails(int lessonId) {
    var endpoint = "${DioProvider.baseUrl}/lesson/$lessonId";
    var dioCall = dioClient.get(
      endpoint,
      options: options.copyWith(policy: CachePolicy.refresh).toOptions(),
    );

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => _parseLessonDetailsResponse(response));
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<LessonData> getLessonList(
      LessonSearchQueryParam lessonSearchQueryParam) {
    var endpoint = "${DioProvider.baseUrl}/lesson";
    var dioCall = dioClient.get(
      endpoint,
      queryParameters: lessonSearchQueryParam.toJson(),
      options: options.copyWith(policy: CachePolicy.refresh).toOptions(),
    );

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => _parseLessonResponse(response));
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<ExamQuizResponseData> getExamQuizListByLesson(int lessonNumber) {
    var endpoint = "${DioProvider.baseUrl}/lesson/getExamQuizes";
    var dioCall = dioClient.get(
      endpoint,
      queryParameters: {"lessonNumber": lessonNumber},
      options: options.copyWith(policy: CachePolicy.refresh).toOptions(),
    );

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => _parseExamQuizResponse(response));
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<PromotionDataResponse> getAllPromotion() {
    var endpoint = "${DioProvider.baseUrl}/lesson/getAllPromotion";
    var dioCall = dioClient.post(endpoint);

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => _parsePromotionResponse(response));
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<GreQuizList> getGreQuizListByLesson(int lessonNumber) {
    var endpoint = "${DioProvider.baseUrl}/lesson/getGreQuizListByLesson";
    var dioCall = dioClient.get(
      endpoint,
      queryParameters: {"lessonNumber": lessonNumber},
      options: options.copyWith(policy: CachePolicy.refresh).toOptions(),
    );

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => _parseGreQuizResponse(response));
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<LessonDiscussionResponse> getAllLessonDiscussion(
      DiscussionQueryPrem discussionQueryPrem) {
    var endpoint = "${DioProvider.baseUrl}/lesson/getQuizDiscussion";
    var dioCall =
        dioClient.get(endpoint, queryParameters: discussionQueryPrem.toJson());

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => LessonDiscussionResponse.fromJson(response.data));
    } catch (e) {
      rethrow;
    }

    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> addLessonDiscussion(
      LessonDiscussionBodyPrem lessonDiscussionBodyPrem) {
    var endpoint = "${DioProvider.baseUrl}/lesson/addQuizDiscussion";
    var dioCall =
        dioClient.post(endpoint, data: lessonDiscussionBodyPrem.toJson());

    try {
      return callApiWithErrorParser(dioCall).then((response) => response.data);
    } catch (e) {
      rethrow;
    }

    throw UnimplementedError();
  }

  @override
  Future<BookResponseData> getBookList() {
    var endpoint = "${DioProvider.baseUrl}/ielts/books";
    var dioCall = dioClient.get(endpoint);

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => _parseGetBookList(response));
    } catch (e) {
      rethrow;
    }

    throw UnimplementedError();
  }

  @override
  Future<ReadingResponseData> getReadingListOfBook(int id) {
    var endpoint = "${DioProvider.baseUrl}/ielts/readingLists";
    var dioCall = dioClient.get(endpoint, queryParameters: {"id": id});

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => _parseReadingListOfBook(response));
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<ReadingPassageDetails> getReadingPassageDetails(int id) {
    var endpoint = "${DioProvider.baseUrl}/ielts/readingPassages/$id";
    var dioCall = dioClient.get(endpoint);

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => _parseReadingPassageDetails(response));
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<ReadingPassageResponseData> getReadingPassageOfReadingList(int id) {
    var endpoint = "${DioProvider.baseUrl}/ielts/readingPassages";
    var dioCall = dioClient.get(endpoint, queryParameters: {"id": id});

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => _parseReadingPassageOfReadingList(response));
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> voteDiscussion(String id, String voteType) {
    var endpoint = "${DioProvider.baseUrl}/lesson/vote";
    var dioCall = dioClient.post(endpoint, data: {
      "discussionId": id,
      "voteType": voteType,
    });

    try {
      return callApiWithErrorParser(dioCall).then((response) => response.data);
    } catch (e) {
      rethrow;
    }

    throw UnimplementedError();
  }
}

_parseReadingPassageOfReadingList(Response response) {
  return ReadingPassageResponseData.fromJson(response.data);
}

_parseReadingPassageDetails(Response response) {
  return ReadingPassageDetails.fromJson(response.data);
}

_parseReadingListOfBook(Response response) {
  return ReadingResponseData.fromJson(response.data);
}

_parseGetBookList(Response response) {
  return BookResponseData.fromJson(response.data);
}

_parsePromotionResponse(Response response) {
  return PromotionDataResponse.fromJson(response.data);
}

_parseExamQuizResponse(Response response) {
  return ExamQuizResponseData.fromJson(response.data);
}

_parseGreQuizResponse(Response response) {
  return GreQuizList.fromJson(response.data);
}

_parseLessonResponse(Response response) {
  return LessonData.fromJson(response.data);
}

_parseLessonDetailsResponse(Response response) {
  return LessonDetailsData.fromJson(response.data);
}
