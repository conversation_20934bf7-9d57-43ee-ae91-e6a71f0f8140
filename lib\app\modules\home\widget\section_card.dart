import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/values/text_styles.dart';

import '../../../core/values/app_values.dart';
import '../../../core/widget/elevated_container.dart';
import '../../../core/widget/ripple.dart';
import '../../free_lesson/model/feature_ui_data.dart';

class SectionCard extends StatelessWidget {
  const SectionCard({super.key, required this.sectionUiData});
  final SectionUiData sectionUiData;
  @override
  Widget build(BuildContext context) {
    return ElevatedContainer(
      bgColor: sectionUiData.bgColor,
      child: Ripple(
        onTap: _onTap,
        child: Padding(
          padding: const EdgeInsets.all(AppValues.padding),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: 200,
                child: Text(sectionUiData.title, style: whiteText18),
              ),
              Image.asset(
                sectionUiData.imageSrc,
                height: AppValues.iconExtraLargerSize,
                width: AppValues.iconExtraLargerSize,
              ),
            ],
          ),
        ),
      ),
    );
  }

  _onTap() {
    Get.toNamed(sectionUiData.routeName);
  }
}
