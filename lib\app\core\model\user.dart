
class UserProfileInfo {
  final String? userName;
  final String? phoneNumber;
  final String? profilePic;
  final String? gmail;
  final int? score;
  final List<String>? completedId;
  final String? userId;
  final bool? subscribe;

  UserProfileInfo({
    this.subscribe,
    this.gmail,
    this.userName,
    this.phoneNumber,
    this.profilePic,
    this.score,
    this.completedId,
    this.userId,
  });

  // Convert the UserProfileInfo object to a Map that can be stored in Firestore.
  Map<String, dynamic> toMap() {
    return {
      'userName': userName,
      'phoneNumber': phoneNumber,
      'profilePic': profilePic,
      'gmail': gmail,
      'score': score,
      'completedId': completedId,
      'userId': userId,
      'subscribe': subscribe,
    };
  }


}
