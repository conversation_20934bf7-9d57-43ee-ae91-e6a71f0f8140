import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/modules/quiz_two/widget/quiz_question_card.dart';
import '../../../core/values/app_values.dart';
import '../controller/quiz_two_controller.dart';

class QuizQuestionsList extends StatelessWidget {
  final QuizTwoController controller;
  final Function(int) onAnswerSubmitted;

  const QuizQuestionsList({
    Key? key,
    required this.controller,
    required this.onAnswerSubmitted,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Padding(
        padding: const EdgeInsets.symmetric(horizontal: AppValues.padding),
        child: ListView.builder(
          itemCount: controller.endIndex.value - controller.startIndex.value,
          itemBuilder:
              (context, index) => QuizQuestionCard(
                question: controller.quiz2[controller.startIndex.value + index],
                textController:
                    controller.textControllers[controller.startIndex.value +
                        index],
                answerState:
                    controller.list[controller.startIndex.value + index],
                onSubmit: () => onAnswerSubmitted(index),
              ),
        ),
      ),
    );
  }
}
