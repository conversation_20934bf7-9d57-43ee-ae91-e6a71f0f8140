import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/values/app_values.dart';
import 'package:news_lexica_app/app/core/widget/custom_app_bar.dart';
import 'package:news_lexica_app/app/routes/app_pages.dart';

import '../controller/exam_quiz_list_controller.dart';

class ExamQuizListView extends BaseView<ExamQuizListController> {
  ExamQuizListView() {
    controller.getUserData();
  }
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return CustomAppBar(appBarTitleText: 'Job Vocabulary Quizzes');
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(AppValues.padding),
      child: ListView.builder(
        itemCount: 140,
        itemBuilder: (context, index) {
          return Card(
            child: ListTile(
              onTap:
                  () =>
                      index < 5
                          ? Get.toNamed(
                            Routes.EXAM_QUIZ_DETAILS,
                            arguments: index + 1,
                          )
                          : controller.isSubscribed.value
                          ? Get.toNamed(
                            Routes.EXAM_QUIZ_DETAILS,
                            arguments: index + 1,
                          )
                          : Fluttertoast.showToast(msg: "Your not subscribed"),
              leading:
                  index < 5
                      ? Icon(Icons.quiz)
                      : Obx(
                        () =>
                            controller.isSubscribed.value
                                ? Icon(Icons.quiz)
                                : Icon(Icons.lock),
                      ),
              title: Text('Quiz ${index + 1}'),
              subtitle: const Text("Previous Years' Job Vocabulary"),
            ),
          );
        },
      ),
    );
    throw UnimplementedError();
  }
}
