class LessonDetailsData {
  Message? message;

  LessonDetailsData({this.message});

  LessonDetailsData.fromJson(Map<String, dynamic> json) {
    message =
    json['message'] != null ? new Message.fromJson(json['message']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.message != null) {
      data['message'] = this.message!.toJson();
    }
    return data;
  }
}

class Message {
  int? id;
  String? createdAt;
  String? updatedAt;
  String? title;
  String? lessonTitle;
  int? lessonNumber;
  String? paragraph;
  int? part;
  int? contentType;
  String? category;
  String? picture;
  String? pressName;
  String? publishDate;
  String? audio;
  List<QuizOne>? quizOne;
  List<QuizTwo>? quizTwo;
  List<QuizThree>? quizThree;

  Message(
      {this.id,
        this.createdAt,
        this.updatedAt,
        this.title,
        this.lessonTitle,
        this.lessonNumber,
        this.paragraph,
        this.part,
        this.contentType,
        this.category,
        this.picture,
        this.pressName,
        this.publishDate,
        this.audio,
        this.quizOne,
        this.quizTwo,
        this.quizThree});

  Message.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    title = json['title'];
    lessonTitle = json['lessonTitle'];
    lessonNumber = json['lessonNumber'];
    paragraph = json['paragraph'];
    part = json['part'];
    contentType = json['contentType'];
    picture = json['picture'];
    category = json['category'];
    pressName = json['pressName'];
    publishDate = json['publishDate'];
    audio = json['audio'];
    if (json['QuizOne'] != null) {
      quizOne = <QuizOne>[];
      json['QuizOne'].forEach((v) {
        quizOne!.add(new QuizOne.fromJson(v));
      });
    }
    if (json['QuizTwo'] != null) {
      quizTwo = <QuizTwo>[];
      json['QuizTwo'].forEach((v) {
        quizTwo!.add(new QuizTwo.fromJson(v));
      });
    }
    if (json['QuizThree'] != null) {
      quizThree = <QuizThree>[];
      json['QuizThree'].forEach((v) {
        quizThree!.add(new QuizThree.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    data['title'] = this.title;
    data['lessonTitle'] = this.lessonTitle;
    data['lessonNumber'] = this.lessonNumber;
    data['paragraph'] = this.paragraph;
    data['part'] = this.part;
    data['category'] = this.category;
    data['contentType'] = this.contentType;
    data['picture'] = this.picture;
    data['pressName'] = this.pressName;
    data['publishDate'] = this.publishDate;
    data['audio'] = this.audio;
    if (this.quizOne != null) {
      data['QuizOne'] = this.quizOne!.map((v) => v.toJson()).toList();
    }
    if (this.quizTwo != null) {
      data['QuizTwo'] = this.quizTwo!.map((v) => v.toJson()).toList();
    }
    if (this.quizThree != null) {
      data['QuizThree'] = this.quizThree!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class QuizOne {
  int? id;
  String? question;
  String? option1;
  String? option2;
  String? option3;
  String? option4;
  String? answer;
  int? lessonId;

  QuizOne(
      {this.id,
        this.question,
        this.option1,
        this.option2,
        this.option3,
        this.option4,
        this.answer,
        this.lessonId});

  QuizOne.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    question = json['question'];
    option1 = json['option1'];
    option2 = json['option2'];
    option3 = json['option3'];
    option4 = json['option4'];
    answer = json['answer'];
    lessonId = json['lessonId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['question'] = this.question;
    data['option1'] = this.option1;
    data['option2'] = this.option2;
    data['option3'] = this.option3;
    data['option4'] = this.option4;
    data['answer'] = this.answer;
    data['lessonId'] = this.lessonId;
    return data;
  }
}

class QuizTwo {
  int? id;
  String? question;
  String? answer;
  int? lessonId;

  QuizTwo({this.id, this.question, this.answer, this.lessonId});

  QuizTwo.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    question = json['question'];
    answer = json['answer'];
    lessonId = json['lessonId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['question'] = this.question;
    data['answer'] = this.answer;
    data['lessonId'] = this.lessonId;
    return data;
  }
}



class QuizThree {
  int? id;
  String? question;
  String? option1;
  String? option2;
  String? option3;
  String? option4;
  String? answer;
  int? lessonId;

  QuizThree(
      {this.id,
        this.question,
        this.option1,
        this.option2,
        this.option3,
        this.option4,
        this.answer,
        this.lessonId});

  QuizThree.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    question = json['question'];
    option1 = json['option1'];
    option2 = json['option2'];
    option3 = json['option3'];
    option4 = json['option4'];
    answer = json['answer'];
    lessonId = json['lessonId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['question'] = this.question;
    data['option1'] = this.option1;
    data['option2'] = this.option2;
    data['option3'] = this.option3;
    data['option4'] = this.option4;
    data['answer'] = this.answer;
    data['lessonId'] = this.lessonId;
    return data;
  }
}
