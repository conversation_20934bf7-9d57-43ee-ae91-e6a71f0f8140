import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';

import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:news_lexica_app/app/core/widget/custom_app_bar.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../../../core/base/base_view.dart';
import '../controller/video_play_controller.dart';

class VideoPLayView extends BaseView<VideoPLayController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return CustomAppBar(appBarTitleText: "Intro Video");
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return YoutubePlayerBuilder(
      player: YoutubePlayer(controller: controller.myController),
      builder: (context, player) {
        return Column(
          children: [
            // some widgets
            player,
            //some other widgets
          ],
        );
      },
    );

    throw UnimplementedError();
  }
}
