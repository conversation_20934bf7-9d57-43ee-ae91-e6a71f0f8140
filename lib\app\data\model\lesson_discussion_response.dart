class LessonDiscussionResponse {
  List<Data>? data;
  int? totalItems;
  int? totalPages;
  int? currentPage;

  LessonDiscussionResponse(
      {this.data, this.totalItems, this.totalPages, this.currentPage});

  LessonDiscussionResponse.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(new Data.fromJson(v));
      });
    }
    totalItems = json['totalItems'];
    totalPages = json['totalPages'];
    currentPage = json['currentPage'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['totalItems'] = this.totalItems;
    data['totalPages'] = this.totalPages;
    data['currentPage'] = this.currentPage;
    return data;
  }
}

class Data {
  int? id;
  int? lessonId;
  int? userId;
  String? comment;
  String? createdAt;
  String? updatedAt;
  Null? parentId;
  User? user;
  List<Votes>? votes;
  List<Replies>? replies;

  Data(
      {this.id,
      this.lessonId,
      this.userId,
      this.comment,
      this.createdAt,
      this.updatedAt,
      this.parentId,
      this.user,
      this.votes,
      this.replies});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    lessonId = json['lessonId'];
    userId = json['userId'];
    comment = json['comment'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    parentId = json['parentId'];
    user = json['user'] != null ? new User.fromJson(json['user']) : null;
    if (json['votes'] != null) {
      votes = <Votes>[];
      json['votes'].forEach((v) {
        votes!.add(new Votes.fromJson(v));
      });
    }
    if (json['replies'] != null) {
      replies = <Replies>[];
      json['replies'].forEach((v) {
        replies!.add(new Replies.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['lessonId'] = this.lessonId;
    data['userId'] = this.userId;
    data['comment'] = this.comment;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    data['parentId'] = this.parentId;
    if (this.user != null) {
      data['user'] = this.user!.toJson();
    }
    if (this.votes != null) {
      data['votes'] = this.votes!.map((v) => v.toJson()).toList();
    }
    if (this.replies != null) {
      data['replies'] = this.replies!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class User {
  int? id;
  String? name;
  String? photo;
  String? password;
  String? phone;
  String? refreshToken;

  User(
      {this.id,
      this.name,
      this.photo,
      this.password,
      this.phone,
      this.refreshToken});

  User.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    photo = json['photo'];
    password = json['password'];
    phone = json['phone'];
    refreshToken = json['refreshToken'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['photo'] = this.photo;
    data['password'] = this.password;
    data['phone'] = this.phone;
    data['refreshToken'] = this.refreshToken;
    return data;
  }
}

class Votes {
  int? id;
  int? userId;
  int? discussionId;
  String? voteType;

  Votes({this.id, this.userId, this.discussionId, this.voteType});

  Votes.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['userId'];
    discussionId = json['discussionId'];
    voteType = json['voteType'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['userId'] = this.userId;
    data['discussionId'] = this.discussionId;
    data['voteType'] = this.voteType;
    return data;
  }
}

class Replies {
  int? id;
  int? lessonId;
  int? userId;
  String? comment;
  String? createdAt;
  String? updatedAt;
  int? parentId;
  User? user;

  Replies(
      {this.id,
      this.lessonId,
      this.userId,
      this.comment,
      this.createdAt,
      this.updatedAt,
      this.parentId,
      this.user});

  Replies.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    lessonId = json['lessonId'];
    userId = json['userId'];
    comment = json['comment'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    parentId = json['parentId'];
    user = json['user'] != null ? new User.fromJson(json['user']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['lessonId'] = this.lessonId;
    data['userId'] = this.userId;
    data['comment'] = this.comment;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    data['parentId'] = this.parentId;
    if (this.user != null) {
      data['user'] = this.user!.toJson();
    }
    return data;
  }
}
