import 'package:news_lexica_app/app/core/base/base_controller.dart';
import 'dart:math';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/state_manager.dart';
import '../../../../core/model/update_user_score_body_prem.dart';
import '../../../../core/values/app_values.dart';
import '../../../../core/values/text_styles.dart';
import '../../../../data/model/reading_passage_details.dart';
import '../../../../data/repository/user_repository.dart';
import '../../quiz1/model/quiz_1_brain.dart';

class IeltsQuiz2Controller extends BaseController {
  final QuizOneBrain quizBrain = QuizOneBrain();
  RxList<String> leftSelected = <String>[].obs;
  RxList<String> rightSelected = <String>[].obs;
  RxList<String> leftCorrected = <String>[].obs;
  RxList<String> rightCorrected = <String>[].obs;
  RxList<String> leftWrong = <String>[].obs;
  RxList<String> rightWrong = <String>[].obs;
  final UserRepository _repositoryUser = Get.find(
    tag: (UserRepository).toString(),
  );
  Random random = Random();
  final player = AudioPlayer();
  void selectLeft(String word) {
    if (leftCorrected.contains(word)) {
    } else {
      if (leftSelected.isNotEmpty) leftSelected.clear();
      leftSelected.add(word);
      checkMatch();
    }
  }

  void selectRight(String word) {
    if (rightCorrected.contains(word)) {
    } else {
      if (rightSelected.isNotEmpty) rightSelected.clear();
      rightSelected.add(word);
      checkMatch();
    }
  }

  Future<void> checkMatch() async {
    if (leftSelected.isNotEmpty && rightSelected.isNotEmpty) {
      final leftWord = leftSelected.first;
      final rightWord = rightSelected.first;

      final isMatch = quizBrain.quizData.any(
        (pair) => pair["question"] == leftWord && pair["answer"] == rightWord,
      );

      if (isMatch) {
        int randomNumber = random.nextInt(12) + 1;
        await player.play(AssetSource('audio/correct${randomNumber}.mp3'));
        leftCorrected.add(leftWord);
        rightCorrected.add(rightWord);
      } else {
        player.play(AssetSource('audio/wrong.mp3'));
        leftWrong.add(leftWord);
        rightWrong.add(rightWord);

        await Future.delayed(const Duration(milliseconds: 500), () {
          leftWrong.remove(leftWord);
          rightWrong.remove(rightWord);
        });
      }

      leftSelected.clear();
      rightSelected.clear();

      if (rightCorrected.length == quizBrain.quizData.length) {
        quizCompleteDialog(rightCorrected.length);
      }
    }
  }

  void quizCompleteDialog(int score) {
    Get.dialog(
      AlertDialog(
        title: Text('Your Score: $score/${quizBrain.quizData.length}'),
        content: SizedBox(
          height: Get.height * .3,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text("Congratulations 👏", style: titleStyle),
              Text("You completed the quiz.", style: subTitleStyle),
              SizedBox(height: AppValues.padding),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              updateScore("$score");
            },
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  updateScore(String score) {
    var updateScoreService = _repositoryUser.updateUserScore(
      UpdateUserScoreBodyPrem(userId: "", score: score),
    );
    callDataService(
      updateScoreService,
      onSuccess: _handleScoreUpdateSuccess,
      onError: _handleScoreUpdateError,
    );
  }

  _handleScoreUpdateSuccess(Map<String, dynamic> response) {
    Get.back();
    Get.back();
  }

  _handleScoreUpdateError(Exception exception) {
    Fluttertoast.showToast(msg: errorMessage);
    Get.back();
    Get.back();
  }

  @override
  void onInit() {
    var arg = Get.arguments;
    if (arg is List<IeltsReadingPassageQuizTwo>) {
      arg.forEach((element) {
        quizBrain.quizData.add({
          "question": "${element.question}",
          "synonym": "${element.option1}",
          "answer": "${element.answer}",
        });
      });
      super.onInit();
    }
  }
}
