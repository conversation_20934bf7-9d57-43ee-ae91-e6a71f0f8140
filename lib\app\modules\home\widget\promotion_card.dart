import 'package:carousel_slider/carousel_slider.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/modules/home/<USER>/home_controller.dart';
import '../../../core/values/app_colors.dart';
import '../../../core/values/app_url.dart';
import '../../../core/values/app_values.dart';
import '../model/promotion_ui_data.dart';

class PromotionCard extends StatelessWidget {
  const PromotionCard({
    super.key,
    required this.promotionList,
    required this.homeController,
  });
  final List<PromotionUiData> promotionList;
  final HomeController homeController;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppValues.halfPadding),
      child: Column(
        children: [
          Material(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 2.0,
            shadowColor: Colors.grey[100]!,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Container(
                width: double.infinity,
                height: 150,
                child: CarouselSlider(
                  items:
                      promotionList.map((promotion) {
                        return Builder(
                          builder: (BuildContext context) {
                            return Container(
                              margin: EdgeInsets.zero,
                              width: MediaQuery.of(context).size.width,
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    SizedBox(
                                      width: Get.width * .6,
                                      child: Text(
                                        promotion.name,
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ),
                                    Image.asset(
                                      height: 80,
                                      width: 80,
                                      promotion.imageSrc,
                                      fit: BoxFit.cover,
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        );
                      }).toList(),
                  options: CarouselOptions(
                    autoPlay: !homeController.isOlderDevice.value,
                    viewportFraction: 1.0,
                    enlargeCenterPage: false,
                    onPageChanged: (index, reason) {
                      homeController.bannerPosition.value = index;
                    },
                  ),
                ),
              ),
            ),
          ),
          Obx(
            () => Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                padding: const EdgeInsets.only(
                  bottom: 8.0,
                  top: AppValues.padding,
                ),
                child: DotsIndicator(
                  dotsCount: promotionList.length,
                  position: homeController.bannerPosition.value,
                  decorator: DotsDecorator(
                    color: Colors.black12, // Inactive color
                    activeColor: AppColors.colorPrimary,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
