import 'package:flutter/material.dart';
import 'package:news_lexica_app/app/modules/new_ielts_module/quiz1/model/ielts_quiz_1_ui_data.dart';

class WordCard extends StatelessWidget {
  const WordCard({super.key, required this.data, required this.isSelected});
  final IeltsQuiz1UiData data;
  final bool isSelected;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue : Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.grey),
      ),
      child: Text(data.word),
    );
  }
}
