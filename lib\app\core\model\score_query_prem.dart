import '/app/core/values/app_values.dart';

class ScoreQueryPrem {

  int perPage;
  int pageNumber;

  ScoreQueryPrem({

    this.perPage = AppValues.defaultPageSize,
    this.pageNumber = AppValues.defaultPageNumber,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['pageSize'] = perPage;
    data['page'] = pageNumber;

    return data;
  }
}
