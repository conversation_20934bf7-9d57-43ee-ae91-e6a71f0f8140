import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/values/app_values.dart';
import 'package:news_lexica_app/app/modules/free_lesson/model/feature_ui_data.dart';
import 'package:news_lexica_app/app/modules/home/<USER>/section_card.dart';
import 'package:news_lexica_app/app/routes/app_pages.dart';
import '../../../core/values/data.dart';
import '../widget/category_card.dart';
import '/app/core/base/base_view.dart';
import '/app/modules/home/<USER>/home_controller.dart';

class HomeView extends BaseView<HomeController> {
  HomeView() {
    controller.readJson();
  }

  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
  }

  @override
  Widget body(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InkWell(
                onTap: () => Get.toNamed(Routes.APP_HEADING_DETAILS),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: AppValues.halfPadding,
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      color: const Color(0xffecab59),
                      border: Border.all(color: Colors.white),
                      borderRadius: BorderRadius.circular(AppValues.radius_12),
                    ),
                    child: const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Column(
                        children: [
                          Text(
                            'Vocabulary শেখার মাস্টার ট্রেইনার News Lexica এ্যাপ এ আপনাকে স্বাগত!',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                            ),
                          ),
                          SizedBox(height: 5),
                          Text(
                            'Practical Method এ সকল লেভেলের Vocabulary অর্জনে যে সকল ফিচার আছে এই এ্যাপ এ...',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              SectionCard(
                sectionUiData: SectionUiData(
                  routeName: Routes.NEW_IELTS_BOOK_LIST,
                  bgColor: Colors.blueAccent,
                  title: 'Cambridge IELTS Vocabulary',
                  imageSrc: 'assets/ielts_home.png',
                ),
              ),
              const SizedBox(height: AppValues.padding),
              SectionCard(
                sectionUiData: SectionUiData(
                  routeName: Routes.EXAM_QUIZ_LIST,
                  bgColor: Colors.deepPurpleAccent,
                  title: 'Job Exam & Admission Test Vocabulary ',
                  imageSrc: 'assets/exam.png',
                ),
              ),
              const SizedBox(height: AppValues.padding),
              SectionCard(
                sectionUiData: SectionUiData(
                  routeName: Routes.GRE_QUIZ_LIST,
                  bgColor: Colors.teal,
                  title: 'GRE, GMAT Vocabulary',
                  imageSrc: 'assets/gre_exam.png',
                ),
              ),
              const SizedBox(height: AppValues.padding),
              SectionCard(
                sectionUiData: SectionUiData(
                  routeName: Routes.FREE_LESSON_VIEW,
                  bgColor: Colors.indigoAccent,
                  title:
                      'Learn Vocabulary with  Newspaper and Magazine Stories',
                  imageSrc: 'assets/lesson_read.png',
                ),
              ),
              const SizedBox(height: AppValues.padding),
              Text(
                "Premium Stories",
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Part 1 (Beginner and Intermediate Level)",
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                  TextButton(
                    onPressed: () {
                      Get.toNamed(Routes.ALL_CATEGORY, arguments: 1);
                    },
                    child: Text("See All"),
                  ),
                ],
              ),
              CarouselSlider(
                options: CarouselOptions(
                  height: 150.0,
                  aspectRatio: 3 / 5,
                  autoPlay: !controller.isOlderDevice.value,
                  enlargeCenterPage: true,
                ),
                items:
                    mainCategory.take(5).map((item) {
                      return Builder(
                        builder: (BuildContext context) {
                          return CatagoryCard(item: item, part: 1);
                        },
                      );
                    }).toList(),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Part 2 (Advanced Level)",
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                  TextButton(
                    onPressed: () {
                      Get.toNamed(Routes.ALL_CATEGORY, arguments: 2);
                    },
                    child: Text("See All"),
                  ),
                ],
              ),
              CarouselSlider(
                options: CarouselOptions(
                  height: 150.0,
                  aspectRatio: 3 / 5,
                  autoPlay: !controller.isOlderDevice.value,
                  enlargeCenterPage: true,
                ),
                items:
                    mainCategory.take(5).map((item) {
                      return Builder(
                        builder: (BuildContext context) {
                          return CatagoryCard(item: item, part: 2);
                        },
                      );
                    }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
