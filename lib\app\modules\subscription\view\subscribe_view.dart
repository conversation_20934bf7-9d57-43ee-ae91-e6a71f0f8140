import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/values/app_values.dart';
import 'package:news_lexica_app/app/core/values/text_styles.dart';
import 'package:news_lexica_app/app/core/widget/custom_app_bar.dart';

import '../../../core/utils/api.dart';
import '../controller/subscribe_controller.dart';

class SubscribeView extends BaseView<SubscribeController> {
  SubscribeView() {
    controller.getUserData();
  }
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return CustomAppBar(appBarTitleText: "Subscription Page");
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: Obx(() => controller.subscribeUiData.createdAt == 'null'
          ? _noSubscribeView(context)
          : _SubscribeView(context)),
    );
    throw UnimplementedError();
  }

  Widget _noSubscribeView(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          Text(
            "Available packages",
            style: titleStyle,
          ),
          Card(
            child: ListTile(
              title: Text("Economy pack"),
              subtitle: Text("BDT 100 3 months"),
              onTap: () => _showMyDialog(context),
            ),
          ),
          Card(
            child: ListTile(
              title: Text("Standard pack"),
              subtitle: Text("BDT 200 6 months"),
              onTap: () => _showMyDialog(context),
            ),
          ),
          Card(
            child: ListTile(
              title: Text("Rich pack"),
              subtitle: Text("BDT 400 1 Year"),
              onTap: () => _showMyDialog(context),
            ),
          ),
          Card(
            child: ListTile(
              title: Text("Ultrarich pack"),
              subtitle: Text("BDT 2000 Life time"),
              onTap: () => _showMyDialog(context),
            ),
          ),
          Divider(),
          Text(
            "Available Payment Methods",
            style: titleStyle,
          ),
          Card(
            child: ListTile(
              leading: Image.asset('assets/bkash.png'),
              title: Text("Bkash"),
              subtitle: Text("payment with bkash is available"),
              onTap: () => _showMyDialog(context),
            ),
          ),
          Card(
            child: ListTile(
              leading: Image.asset('assets/rocket.png'),
              title: Text("Rocket"),
              subtitle: Text("Payment with rocket available"),
              onTap: () => _showMyDialog(context),
            ),
          ),
          Card(
            child: ListTile(
              leading: Image.asset('assets/upay.png'),
              title: Text("Upay"),
              subtitle: Text("Payment with upay available"),
              onTap: () => _showMyDialog(context),
            ),
          ),
          Divider(),
          Text(
            "For Subscription info",
            style: titleStyle,
          ),
          Card(
            child: ListTile(
              onTap: () => Api().launchWhatsApp(
                  phoneNumber: "+8801860507913",
                  message: 'How can I subscribe to this app'),
              leading: Icon(Icons.chat),
              title: Text(
                "Talk To support",
              ),
              trailing: Icon(Icons.keyboard_arrow_right),
            ),
          ),
        ],
      ),
    );
  }

  Widget _SubscribeView(BuildContext context) {
    return Obx(() => Center(
          child: Column(
            children: [
              SizedBox(
                height: AppValues.padding,
              ),
              Text(
                "Subscription",
                style: Theme.of(context).textTheme.titleLarge,
              ),
              Card(
                child: ListTile(
                  title: Text(
                    "Subscription status",
                    style: whiteText18,
                  ),
                  subtitle: Text(
                    controller.subscribeUiData.subscribed
                        ? "Congrats! You are our valuable subscriber"
                        : "Your subscription is ${controller.subscribeUiData.status.toLowerCase()}",
                    style: whiteText16,
                  ),
                ),
              ),
              Divider(),
              Card(
                child: ListTile(
                  title: Text(
                    "Subscription Expiration Date",
                    style: whiteText18,
                  ),
                  subtitle: Text(
                    "${controller.subscribeUiData.expirationDate}",
                    style: whiteText16,
                  ),
                ),
              ),
            ],
          ),
        ));
  }

  Future<void> _showMyDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Subscription'),
          content: const SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text('Head back to our website for subscription'),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Okay'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }
}
