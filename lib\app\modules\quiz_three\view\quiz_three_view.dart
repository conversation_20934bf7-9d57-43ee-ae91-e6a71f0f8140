import 'dart:math';

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/values/app_values.dart';
import '../../../core/base/base_view.dart';
import '../../../core/values/text_styles.dart';
import '../controller/quiz_three_controller.dart';
import '../widget/checkbox.dart';

class QuizThreeView extends BaseView<QuizThreeController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return PopScope(
      onPopInvoked: (bool didPop) {
        if (didPop) {
          return;
        }
        closeDialog();
      },
      canPop: false,
      child: Column(
        children: [
          Obx(
            () => Text(
              "Score ${controller.list.where((p0) => p0 == Answer.right).length}/${controller.list.length}",
              style: Theme.of(context).textTheme.titleLarge,
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppValues.padding),
            child: Text(
              "Tap the Correct Answers in the multiple choice questions:",
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppValues.padding,
              ),
              child: ListView.builder(
                itemCount:
                    controller.endIndex.value - controller.startIndex.value,
                itemBuilder: (context, index) {
                  //    final quizQuestion = quizController.quiz3[quizController.startIndex.value+index];
                  return Obx(
                    () => Card(
                      color:
                          controller.list[controller.startIndex.value +
                                      index] ==
                                  Answer.right
                              ? Colors.green[200]
                              : controller.list[controller.startIndex.value +
                                      index] ==
                                  Answer.wrong
                              ? Colors.red[200]
                              : Theme.of(context).cardColor,
                      elevation: 3,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "${controller.quiz3[controller.startIndex.value + index].question}",
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            SizedBox(height: 16),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children:
                                  controller
                                      .quiz3[controller.startIndex.value +
                                          index]
                                      .choices
                                      .asMap()
                                      .entries
                                      .map(
                                        (entry) => Padding(
                                          padding: const EdgeInsets.symmetric(
                                            vertical: 6.0,
                                          ),
                                          child: Row(
                                            children: [
                                              Text(
                                                entry.key == 0
                                                    ? "A)"
                                                    : entry.key == 1
                                                    ? "B)"
                                                    : entry.key == 2
                                                    ? "C)"
                                                    : "D)",
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 16,
                                                  color: Colors.blueGrey[700],
                                                ),
                                              ),
                                              SizedBox(width: 12),
                                              Expanded(
                                                child: CustomCheckboxTile(
                                                  title: entry.value,
                                                  value: controller
                                                      .isChoiceSelected(
                                                        controller
                                                                .startIndex
                                                                .value +
                                                            index,
                                                        entry.key,
                                                      ),
                                                  onChanged: (
                                                    bool value,
                                                  ) async {
                                                    int randomNumber =
                                                        controller.random
                                                            .nextInt(12) +
                                                        1;
                                                    if (controller
                                                            .quiz3[controller
                                                                    .startIndex
                                                                    .value +
                                                                index]
                                                            .selectedChoice ==
                                                        null) {
                                                      controller.toggleChoice(
                                                        controller
                                                                .startIndex
                                                                .value +
                                                            index,
                                                        entry.key,
                                                      );
                                                      if (controller
                                                          .quizCheckAnswer(
                                                            entry.value,
                                                            controller
                                                                .quiz3[controller
                                                                    .startIndex
                                                                    .value +
                                                                index],
                                                          )) {
                                                        controller
                                                                .list[controller
                                                                    .startIndex
                                                                    .value +
                                                                index] =
                                                            Answer.right;
                                                        await controller.player
                                                            .play(
                                                              AssetSource(
                                                                'audio/correct${randomNumber}.mp3',
                                                              ),
                                                            );
                                                      } else {
                                                        controller
                                                                .list[controller
                                                                    .startIndex
                                                                    .value +
                                                                index] =
                                                            Answer.wrong;
                                                        await controller.player
                                                            .play(
                                                              AssetSource(
                                                                'audio/wrong.mp3',
                                                              ),
                                                            );
                                                      }
                                                    }
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      )
                                      .toList(),
                            ),
                            SizedBox(height: 20),
                            controller.list[controller.startIndex.value +
                                        index] ==
                                    Answer.wrong
                                ? Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Correct Answer: ${controller.quiz3[controller.startIndex.value + index].answer}",
                                      style: TextStyle(
                                        color: Colors.red[900],
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      "Try again later",
                                      style: TextStyle(
                                        color: Colors.red[700],
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  ],
                                )
                                : Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Excellent",
                                      style: TextStyle(
                                        color: Colors.green[900],
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                      side: BorderSide(color: Colors.teal),
                    ),
                    minimumSize: Size(Get.width * .45, 50),
                  ),
                  onPressed: () {
                    controller.previousQuestion();

                    if (controller.startIndex.value >=
                        controller.quiz3.length) {
                      Get.back();
                    }
                  },
                  child: Text(
                    "Previous Page",
                    style: TextStyle(color: Colors.teal),
                  ),
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    backgroundColor: Colors.teal,
                    minimumSize: Size(Get.width * .45, 50),
                  ),
                  onPressed: () {
                    controller.getNextQuestion();

                    if (controller.startIndex.value >=
                        controller.quiz3.length) {
                      quizCompleteDialog(
                        controller.list
                            .where((p0) => p0 == Answer.right)
                            .length,
                      );
                    }
                  },
                  child: Text(
                    "Next Page",
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
    throw UnimplementedError();
  }

  void quizCompleteDialog(int score) {
    Get.dialog(
      AlertDialog(
        title: Text('Your Score: $score/${controller.list.length}'),
        content: SizedBox(
          height: Get.height * .3,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text("Congratulations 👏", style: titleStyle),
              Text("You completed the quiz.", style: subTitleStyle),
              SizedBox(height: AppValues.padding),
              Text(
                controller.list.where((p0) => p0 == Answer.wrong).length > 1
                    ? "Take the quiz again to correct your mistake."
                    : "",
                style: subTitleStyle,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              controller.updateScore(
                "${controller.list.where((p0) => p0 == Answer.right).length}",
              );
            },
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  void closeDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Quiz'),
        content: const Text('Are you sure you want to quit the quiz?'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text('No'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.back();
            },
            child: Text('yes'),
          ),
        ],
      ),
    );
  }
}
