import 'package:flutter/cupertino.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import 'package:news_lexica_app/app/core/values/app_values.dart';

import '../controller/server_off_controller.dart';

class ServerOffView extends BaseView<ServerOffController>{
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return Center(child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
      Image.asset('images/wired-flat-45-clock-time.gif',height: 100,width: 100,),

        SizedBox(height: AppValues.padding,),
        SizedBox(height: AppValues.padding,),
        const Text("We are on maintenance break"),
        SizedBox(height: AppValues.padding,),
        const Text("We will be back soon"),
    ],),);
    throw UnimplementedError();
  }
  
}