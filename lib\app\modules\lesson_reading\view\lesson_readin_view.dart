import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';

import '../../../core/utils/api.dart';
import '../controller/lesson_reading_controller.dart';

class LessonReadingView extends BaseView<LessonReadingController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Row(
                children: [
                  Image.asset("assets/listen.png", height: 30),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      "Listen to the story",
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  ),
                ],
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                IconButton(
                  onPressed: () => controller.backward10Seconds(),
                  icon: Icon(Icons.replay_10_rounded),
                ),
                Obx(
                  () => IconButton(
                    icon: Icon(
                      controller.isPlaying.value
                          ? Icons.pause
                          : Icons.play_arrow,
                    ),
                    onPressed: () => controller.togglePlayPause(),
                  ),
                ),
                IconButton(
                  onPressed: () => controller..forward10Seconds(),
                  icon: Icon(Icons.forward_10_rounded),
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Obx(
                  () => Wrap(
                    children: [
                      Text(
                        '${controller.position.value.inMinutes}:${(controller.position.value.inSeconds % 60).toString().padLeft(2, '0')}',
                      ),
                      Text("/"),
                      Text(
                        '${controller.duration.value.inMinutes}:${(controller.duration.value.inSeconds % 60).toString().padLeft(2, '0')}',
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Obx(
                    () => Slider(
                      onChanged: (value) {
                        controller.sliderValue.value = value;
                        controller.audioPlayer.seek(
                          Duration(seconds: value.toInt()),
                        );
                      },
                      inactiveColor: Colors.grey,
                      value: controller.sliderValue.value,
                      min: 0.0,
                      max: controller.duration.value.inSeconds.toDouble(),
                    ),
                  ),
                ),
                Icon(Icons.volume_down),
              ],
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                "#${controller.lessonUiData.title}",
                style: Theme.of(context).textTheme.titleLarge!.copyWith(
                  color: Colors.purple,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                "${controller.lessonUiData.pressName}",
                style: Theme.of(context).textTheme.titleMedium!.copyWith(
                  color: Colors.blue,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text("${controller.lessonUiData.publishDate}"),
            ),
            Card(
              color: Colors.blue,
              child: Padding(
                padding: const EdgeInsets.all(5),
                child: Text(
                  'উচ্চারণ শুনতে ও বিস্তারিত জানতে আন্ডারলাইণ্ড ওয়ার্ড ট্যাপ করুন',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            SizedBox(height: 8),
            Html(
              data: controller.lessonUiData.paragraph,
              onLinkTap: (
                String? url,
                Map<String, String> attributes,
                element,
              ) {
                Api().launchURL(url!);
              },
            ),
            SizedBox(height: 50),
            Row(
              children: [
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    shape: BeveledRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    backgroundColor: Colors.teal,
                    minimumSize: Size(Get.width * .9, 50),
                  ),
                  onPressed: () {
                    Get.back();
                  },
                  child: Text(
                    "এখন দেখুন উপরের কতটি Word আপনার মনে আছে",
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
    throw UnimplementedError();
  }
}
