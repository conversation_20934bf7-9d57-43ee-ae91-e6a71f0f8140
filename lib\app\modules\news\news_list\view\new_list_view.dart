import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:news_lexica_app/app/core/values/app_colors.dart';

import '../../../../core/base/base_view.dart';
import '../controller/news_list_controller.dart';
import '../widget/news_list_card.dart';

class NewListView extends BaseView<NewsListController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return DefaultTabController(
        length: 5,
        child: Scaffold(
            appBar: AppBar(
                bottom: const TabBar(
              tabs: [
                Tab(
                  child: Text(
                    'English Newspaper',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                ),
                Tab(
                  icon: Text(
                    'English Magazine',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                ),
              ],
            )),
            body: Tab<PERSON><PERSON><PERSON>iew(
              children: [
                Column(
                  children: [
                    Container(
                      height: 100,
                      color: Color(0xffA64D79),
                      child: Center(
                        child: Text(
                          'English Newspaper around the Globe',
                          style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 20,
                              color: AppColors.colorWhite),
                        ),
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(
                          itemCount: controller.newsList.length,
                          itemBuilder: (context, index) {
                            return NewsListCard(
                              newsListDataModel: controller.newsList[index],
                            );
                          }),
                    ),
                  ],
                ),
                Column(
                  children: [
                    Container(
                      height: 100,
                      color: Color(0xffA64D79),
                      child: Center(
                        child: Text(
                          'English Magazine around the Globe',
                          style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 20,
                              color: AppColors.colorWhite),
                        ),
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(
                          itemCount: controller.magazineList.length,
                          itemBuilder: (context, index) {
                            return NewsListCard(
                              newsListDataModel: controller.magazineList[index],
                            );
                          }),
                    ),
                  ],
                ),
              ],
            )));
    throw UnimplementedError();
  }
}
