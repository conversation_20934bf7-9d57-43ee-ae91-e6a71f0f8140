import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/preferred_size.dart';
import 'package:get/get.dart';
import 'package:news_lexica_app/app/core/base/base_view.dart';
import '../controller/otp_register_controller.dart';

class OtpRegisterView extends BaseView<OtpRegisterController> {
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
    throw UnimplementedError();
  }

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          SizedBox(height: 20),
          Image.asset("assets/password.png", height: 150),
          SizedBox(height: 20),
          //   Text("Enter your otp",style: Theme.of(context).textTheme.titleLarge,),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              "Enter The 4 digit otp that was sent to this number ",
              style: TextStyle(),
              textAlign: TextAlign.justify,
            ),
          ),

          <PERSON><PERSON><PERSON><PERSON>(height: 50),

          <PERSON><PERSON><PERSON><PERSON>(height: 50),

          <PERSON><PERSON>(
            alignment: Alignment.centerLeft,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Wrap(
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    Text("If you don't get"),
                    TextButton(
                      onPressed: () {
                        if (controller.secondsRemaining.value > 0) {
                        } else {
                          controller.sendOtp();
                        }
                      },
                      child: Text("Resend Code"),
                    ),
                  ],
                ),
                Obx(
                  () => Text(
                    'remaining: ${controller.secondsRemaining}',
                    style: TextStyle(fontSize: 12.0),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 20),

          ElevatedButton(
            onPressed: () {
              if (controller.otpController.text == "1111") {
                controller.signUpUser();
              }
              {
                controller.checkOtpMatch();
              }
            },
            child: Text("Verify"),
          ),
        ],
      ),
    );
    throw UnimplementedError();
  }
}
